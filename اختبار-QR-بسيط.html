<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار QR Code البسيط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #075E54, #25D366);
            color: white;
            text-align: center;
            padding: 20px;
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 20px;
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .btn {
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
        }
        
        .qr-container {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        #console {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 10px;
            text-align: left;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            font-size: 0.9rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading {
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار QR Code البسيط</h1>
        <p>هذا اختبار مبسط لتشخيص مشاكل QR Code</p>
        
        <div id="libraryStatus" class="status info">
            🔄 جاري فحص مكتبة QR Code...
        </div>
        
        <button class="btn" onclick="testQRGeneration()">📱 اختبار إنشاء QR Code</button>
        <button class="btn" onclick="clearTest()">🗑️ مسح الاختبار</button>
        
        <div class="qr-container" id="qrContainer">
            <div id="qrPlaceholder">
                <div style="font-size: 3rem;">📱</div>
                <p>اضغط "اختبار إنشاء QR Code" لبدء الاختبار</p>
            </div>
            <canvas id="qrCanvas" style="display: none;"></canvas>
        </div>
        
        <div id="testStatus" class="status info" style="display: none;">
            ⏳ جاري الاختبار...
        </div>
        
        <h3>📋 سجل التشخيص:</h3>
        <div id="console"></div>
        
        <div style="margin-top: 20px; font-size: 0.9rem; color: #666;">
            <p><strong>كيفية الاستخدام:</strong></p>
            <ol style="text-align: right;">
                <li>راقب حالة مكتبة QR Code أعلاه</li>
                <li>اضغط "اختبار إنشاء QR Code"</li>
                <li>راقب سجل التشخيص</li>
                <li>تحقق من ظهور QR Code</li>
            </ol>
        </div>
    </div>

    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    
    <script>
        let consoleDiv = document.getElementById('console');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : type === 'success' ? '#0f0' : '#fff';
            consoleDiv.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        // Test QR Code library on load
        window.onload = function() {
            log('🔄 بدء فحص مكتبة QR Code...');
            
            setTimeout(() => {
                if (typeof QRCode === 'undefined') {
                    log('❌ مكتبة QR Code غير محملة!', 'error');
                    updateStatus('libraryStatus', '❌ مكتبة QR Code غير محملة', 'error');
                    
                    // Try alternative CDN
                    log('🔄 محاولة تحميل من CDN بديل...', 'info');
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/qrcode@1.5.3/build/qrcode.min.js';
                    script.onload = function() {
                        log('✅ تم تحميل مكتبة QR Code من CDN بديل', 'success');
                        updateStatus('libraryStatus', '✅ مكتبة QR Code محملة (CDN بديل)', 'success');
                    };
                    script.onerror = function() {
                        log('❌ فشل تحميل مكتبة QR Code من جميع المصادر', 'error');
                        updateStatus('libraryStatus', '❌ فشل تحميل مكتبة QR Code', 'error');
                    };
                    document.head.appendChild(script);
                } else {
                    log('✅ مكتبة QR Code محملة بنجاح', 'success');
                    log(`📋 نوع المكتبة: ${typeof QRCode}`, 'info');
                    updateStatus('libraryStatus', '✅ مكتبة QR Code جاهزة', 'success');
                }
            }, 1000);
        };
        
        function testQRGeneration() {
            log('🔄 بدء اختبار إنشاء QR Code...', 'info');
            
            const canvas = document.getElementById('qrCanvas');
            const placeholder = document.getElementById('qrPlaceholder');
            const testStatus = document.getElementById('testStatus');
            
            // Show test status
            testStatus.style.display = 'block';
            updateStatus('testStatus', '⏳ جاري اختبار إنشاء QR Code...', 'info');
            
            // Check if library is loaded
            if (typeof QRCode === 'undefined') {
                log('❌ لا يمكن إنشاء QR Code - المكتبة غير محملة', 'error');
                updateStatus('testStatus', '❌ فشل الاختبار - المكتبة غير محملة', 'error');
                return;
            }
            
            // Hide placeholder
            placeholder.style.display = 'none';
            
            // Test data
            const testData = `https://wa.me/qr/TEST${Date.now()}`;
            log(`📱 بيانات الاختبار: ${testData}`, 'info');
            
            try {
                QRCode.toCanvas(canvas, testData, {
                    width: 250,
                    height: 250,
                    margin: 2,
                    color: {
                        dark: '#075E54',
                        light: '#FFFFFF'
                    },
                    errorCorrectionLevel: 'M'
                }, function (error) {
                    if (error) {
                        log(`❌ خطأ في إنشاء QR Code: ${error.message}`, 'error');
                        updateStatus('testStatus', '❌ فشل إنشاء QR Code', 'error');
                        placeholder.style.display = 'block';
                        canvas.style.display = 'none';
                    } else {
                        log('✅ تم إنشاء QR Code بنجاح!', 'success');
                        updateStatus('testStatus', '✅ نجح الاختبار - QR Code تم إنشاؤه', 'success');
                        canvas.style.display = 'block';
                        canvas.style.border = '3px solid #25D366';
                        canvas.style.borderRadius = '10px';
                        log('🎉 الاختبار مكتمل بنجاح!', 'success');
                    }
                });
            } catch (e) {
                log(`❌ استثناء في إنشاء QR Code: ${e.message}`, 'error');
                updateStatus('testStatus', '❌ خطأ في النظام', 'error');
                placeholder.style.display = 'block';
                canvas.style.display = 'none';
            }
        }
        
        function clearTest() {
            log('🗑️ مسح نتائج الاختبار...', 'info');
            
            document.getElementById('qrCanvas').style.display = 'none';
            document.getElementById('qrPlaceholder').style.display = 'block';
            document.getElementById('testStatus').style.display = 'none';
            
            log('✅ تم مسح الاختبار', 'success');
        }
    </script>
</body>
</html>
