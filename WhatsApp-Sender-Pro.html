<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sender Pro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #075E54 0%, #128C7E 50%, #25D366 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #075E54, #128C7E);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab.active {
            background: white;
            border-bottom-color: #25D366;
            color: #075E54;
            font-weight: bold;
        }

        .tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #25D366;
            box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .card h3 {
            color: #075E54;
            margin-bottom: 15px;
        }

        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #25D366, #128C7E);
            width: 0%;
            transition: width 0.3s ease;
        }

        .number-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }

        .number-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }

        .number-item:last-child {
            border-bottom: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }

        /* Connection Method Styles */
        .connection-method {
            transition: all 0.3s ease;
        }

        .connection-method:hover {
            border-color: #25D366 !important;
            background: rgba(37, 211, 102, 0.05);
        }

        .connection-method input[type="radio"]:checked + div {
            color: #075E54;
        }

        .connection-method:has(input[type="radio"]:checked) {
            border-color: #25D366 !important;
            background: rgba(37, 211, 102, 0.1);
        }

        /* Phone Connection Status */
        .connection-status {
            transition: all 0.3s ease;
        }

        .connection-status.connected {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .connection-status.connecting {
            border-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }

        .connection-status.disconnected {
            border-color: #ddd;
            background: #f8f9fa;
        }

        /* QR Code Animation */
        @keyframes qrPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .qr-generating {
            animation: qrPulse 2s infinite;
        }

        /* Loading Animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
            }

            .grid {
                grid-template-columns: 1fr;
            }

            .container {
                margin: 10px;
                border-radius: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WhatsApp Sender Pro</h1>
            <p>أداة احترافية لإرسال رسائل WhatsApp بسهولة وفعالية</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('api')">⚙️ إعدادات API</button>
            <button class="tab" onclick="showTab('qr')">📱 QR إرسال سريع</button>
            <button class="tab" onclick="showTab('single')">📱 رسالة واحدة</button>
            <button class="tab" onclick="showTab('bulk')">📊 رسائل جماعية</button>
            <button class="tab" onclick="showTab('groups')">👥 مجموعات الأرقام</button>
        </div>

        <!-- API Settings Tab -->
        <div id="api" class="tab-content active">
            <!-- Connection Method Selection -->
            <div class="card" style="margin-bottom: 20px;">
                <h3>اختر طريقة الاتصال</h3>
                <div style="display: flex; gap: 20px; margin: 20px 0;">
                    <label style="display: flex; align-items: center; cursor: pointer; padding: 15px; border: 2px solid #ddd; border-radius: 10px; flex: 1; transition: all 0.3s ease;">
                        <input type="radio" name="connectionMethod" value="api" checked onchange="switchConnectionMethod('api')" style="margin-left: 10px;">
                        <div>
                            <strong>🔗 Green API</strong>
                            <br><small>استخدام API للإرسال التلقائي</small>
                        </div>
                    </label>
                    <label style="display: flex; align-items: center; cursor: pointer; padding: 15px; border: 2px solid #ddd; border-radius: 10px; flex: 1; transition: all 0.3s ease;">
                        <input type="radio" name="connectionMethod" value="phone" onchange="switchConnectionMethod('phone')" style="margin-left: 10px;">
                        <div>
                            <strong>📱 هاتف المرسل</strong>
                            <br><small>ربط هاتفك مباشرة عبر QR Code</small>
                        </div>
                    </label>
                </div>
            </div>

            <!-- API Settings Section -->
            <div id="apiSettings" class="grid">
                <div class="card">
                    <h3>إعدادات Green API</h3>
                    <div class="form-group">
                        <label>Instance ID:</label>
                        <input type="text" id="instanceId" placeholder="أدخل Instance ID">
                    </div>
                    <div class="form-group">
                        <label>API Token:</label>
                        <input type="password" id="apiToken" placeholder="أدخل API Token">
                    </div>
                    <button class="btn" onclick="saveSettings()">💾 حفظ الإعدادات</button>
                    <button class="btn btn-secondary" onclick="testConnection()">🔍 اختبار الاتصال</button>
                </div>

                <div class="card">
                    <h3>كيفية الحصول على بيانات API</h3>
                    <ol style="line-height: 1.8;">
                        <li>اذهب إلى: <a href="https://green-api.com" target="_blank">green-api.com</a></li>
                        <li>سجل حساب مجاني</li>
                        <li>أنشئ Instance جديد</li>
                        <li>انسخ Instance ID و API Token</li>
                        <li>الصقهما في الحقول أعلاه</li>
                        <li>امسح QR Code لربط WhatsApp</li>
                    </ol>
                </div>
            </div>

            <!-- Phone Connection Section -->
            <div id="phoneSettings" class="grid" style="display: none;">
                <div class="card">
                    <h3>ربط هاتف المرسل</h3>
                    <div id="phoneConnectionStatus" class="form-group">
                        <div style="text-align: center; padding: 20px; border: 2px dashed #ddd; border-radius: 10px;">
                            <div id="connectionStatusIcon" style="font-size: 3rem; margin-bottom: 10px;">📱</div>
                            <div id="connectionStatusText" style="font-size: 1.2rem; font-weight: bold; color: #666;">غير متصل</div>
                            <div id="connectionStatusDesc" style="color: #999; margin-top: 5px;">اضغط "ربط الهاتف" لبدء الاتصال</div>
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <button class="btn" id="connectPhoneBtn" onclick="connectPhone()">📱 ربط الهاتف</button>
                        <button class="btn btn-secondary" id="confirmConnectionBtn" onclick="confirmConnection()" style="display: none;">✅ تأكيد الربط</button>
                        <button class="btn btn-danger" id="disconnectPhoneBtn" onclick="disconnectPhone()" style="display: none;">🔌 قطع الاتصال</button>
                    </div>
                </div>

                <div class="card">
                    <h3>ربط WhatsApp Web</h3>
                    <div id="phoneQRContainer" style="text-align: center; padding: 20px;">
                        <div id="phoneQRPlaceholder" style="border: 2px dashed #ddd; padding: 40px; border-radius: 10px; color: #999;">
                            <i style="font-size: 3rem;">📱</i>
                            <p>اضغط "ربط الهاتف" لفتح WhatsApp Web</p>
                        </div>
                        <div id="whatsappWebContainer" style="display: none; border: 2px solid #25D366; padding: 20px; border-radius: 10px; background: rgba(37, 211, 102, 0.1);">
                            <div style="font-size: 2rem; margin-bottom: 10px;">🌐</div>
                            <h4 style="color: #075E54; margin-bottom: 15px;">WhatsApp Web</h4>
                            <p style="margin-bottom: 15px;">سيتم فتح WhatsApp Web في نافذة جديدة</p>
                            <button class="btn" onclick="openWhatsAppWeb()" style="margin: 5px;">🌐 فتح WhatsApp Web</button>
                            <button class="btn btn-secondary" onclick="copyWhatsAppWebLink()" style="margin: 5px;">📋 نسخ الرابط</button>
                        </div>
                    </div>
                    <div id="phoneQRInstructions" style="display: none; margin-top: 15px; font-size: 0.9rem; color: #666;">
                        <p><strong>خطوات الربط:</strong></p>
                        <ol style="text-align: right; line-height: 1.6;">
                            <li>اضغط "فتح WhatsApp Web" أعلاه</li>
                            <li>سيفتح WhatsApp Web في نافذة جديدة</li>
                            <li>امسح QR Code الظاهر في WhatsApp Web بهاتفك</li>
                            <li>ارجع لهذا التطبيق واضغط "تأكيد الربط"</li>
                            <li>ابدأ الإرسال من أي تبويب</li>
                        </ol>
                    </div>
                </div>
            </div>

            <div id="apiAlert" class="alert"></div>
        </div>

        <!-- QR Code Quick Send Tab -->
        <div id="qr" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>إرسال سريع عبر QR Code</h3>
                    <div class="form-group">
                        <label>رقم الهاتف (مع كود الدولة):</label>
                        <input type="text" id="qrPhone" placeholder="مثال: 201234567890" oninput="generateQR()">
                    </div>
                    <div class="form-group">
                        <label>الرسالة:</label>
                        <textarea id="qrMessage" rows="4" placeholder="اكتب رسالتك هنا..." oninput="generateQR()"></textarea>
                    </div>
                    <div class="form-group">
                        <label>إضافة موقع الويب:</label>
                        <input type="url" id="qrWebsite" placeholder="https://example.com" oninput="generateQR()">
                        <small>اختياري - سيتم إضافة الرابط في نهاية الرسالة</small>
                    </div>
                    <button class="btn" onclick="sendViaQR()">📱 إرسال عبر WhatsApp</button>
                    <button class="btn btn-secondary" onclick="copyQRLink()">📋 نسخ الرابط</button>
                </div>

                <div class="card">
                    <h3>QR Code للإرسال المباشر</h3>
                    <div id="qrCodeContainer" style="text-align: center; padding: 20px;">
                        <div id="qrPlaceholder" style="border: 2px dashed #ddd; padding: 40px; border-radius: 10px; color: #999;">
                            <i style="font-size: 3rem;">📱</i>
                            <p>أدخل رقم الهاتف والرسالة لإنشاء QR Code</p>
                        </div>
                        <canvas id="qrCanvas" style="display: none; max-width: 100%; border: 1px solid #ddd; border-radius: 10px;"></canvas>
                    </div>
                    <div style="margin-top: 15px; text-align: center;">
                        <button class="btn btn-secondary" onclick="downloadQR()" id="downloadBtn" style="display: none;">💾 تحميل QR Code</button>
                        <button class="btn btn-secondary" onclick="printQR()" id="printBtn" style="display: none;">🖨️ طباعة QR Code</button>
                    </div>
                    <div style="margin-top: 15px; font-size: 0.9rem; color: #666;">
                        <p><strong>كيفية الاستخدام:</strong></p>
                        <ol style="text-align: right; line-height: 1.6;">
                            <li>امسح QR Code بكاميرا الهاتف</li>
                            <li>سيفتح WhatsApp تلقائياً</li>
                            <li>الرسالة ستكون جاهزة للإرسال</li>
                            <li>اضغط إرسال في WhatsApp</li>
                        </ol>
                    </div>
                </div>
            </div>

            <div class="card" style="margin-top: 20px;">
                <h3>روابط سريعة للإرسال</h3>
                <div id="quickLinks" style="display: none;">
                    <div class="form-group">
                        <label>رابط WhatsApp المباشر:</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" id="whatsappLink" readonly style="flex: 1;">
                            <button class="btn btn-secondary" onclick="copyLink('whatsappLink')">📋 نسخ</button>
                            <button class="btn btn-secondary" onclick="openLink('whatsappLink')">🔗 فتح</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>رابط مختصر للمشاركة:</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" id="shortLink" readonly style="flex: 1;">
                            <button class="btn btn-secondary" onclick="copyLink('shortLink')">📋 نسخ</button>
                            <button class="btn btn-secondary" onclick="shareLink()">📤 مشاركة</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="qrAlert" class="alert"></div>
        </div>

        <!-- Single Message Tab -->
        <div id="single" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>إرسال رسالة واحدة</h3>
                    <div class="form-group">
                        <label>رقم الهاتف (مع كود الدولة):</label>
                        <input type="text" id="singlePhone" placeholder="مثال: 201234567890">
                        <small>أدخل الرقم بدون + أو 00</small>
                    </div>
                    <div class="form-group">
                        <label>الرسالة:</label>
                        <textarea id="singleMessage" rows="5" placeholder="اكتب رسالتك هنا..."></textarea>
                    </div>
                    <button class="btn" onclick="sendSingleMessage()">📤 إرسال الرسالة</button>
                </div>

                <div class="card">
                    <h3>قوالب الرسائل</h3>
                    <select id="messageTemplates" onchange="loadTemplate()">
                        <option value="">اختر قالب...</option>
                        <option value="welcome">رسالة ترحيب</option>
                        <option value="offer">عرض خاص</option>
                        <option value="reminder">تذكير</option>
                        <option value="thanks">شكر</option>
                    </select>
                    <div style="margin-top: 15px;">
                        <button class="btn btn-secondary" onclick="previewMessage()">👁️ معاينة</button>
                        <button class="btn btn-secondary" onclick="clearMessage()">🗑️ مسح</button>
                    </div>
                </div>
            </div>
            <div id="singleAlert" class="alert"></div>
        </div>

        <!-- Bulk Messages Tab -->
        <div id="bulk" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>أرقام الهواتف</h3>
                    <div class="form-group">
                        <label>أدخل الأرقام (رقم في كل سطر):</label>
                        <textarea id="bulkNumbers" rows="8" placeholder="201234567890&#10;201987654321&#10;201555666777"></textarea>
                    </div>
                    <button class="btn btn-secondary" onclick="loadNumbersFromFile()">📁 تحميل من ملف</button>
                    <button class="btn btn-secondary" onclick="loadFromGroup()">👥 تحميل من مجموعة</button>
                </div>

                <div class="card">
                    <h3>الرسالة والإعدادات</h3>
                    <div class="form-group">
                        <label>الرسالة:</label>
                        <textarea id="bulkMessage" rows="5" placeholder="اكتب رسالتك الجماعية هنا..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>التأخير بين الرسائل (ثواني):</label>
                        <input type="number" id="delaySeconds" value="3" min="1" max="60">
                    </div>
                    <button class="btn" onclick="sendBulkMessages()">📤 إرسال جماعي</button>
                    <div class="progress" id="bulkProgress">
                        <div class="progress-bar" id="bulkProgressBar"></div>
                    </div>
                    <div id="bulkStatus" style="margin-top: 10px; font-weight: bold;"></div>
                </div>
            </div>
            <div id="bulkAlert" class="alert"></div>
        </div>

        <!-- Number Groups Tab -->
        <div id="groups" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>إنشاء مجموعة جديدة</h3>
                    <div class="form-group">
                        <label>اسم المجموعة:</label>
                        <input type="text" id="groupName" placeholder="مثال: عملاء-VIP">
                    </div>
                    <div class="form-group">
                        <label>أرقام المجموعة:</label>
                        <textarea id="groupNumbers" rows="6" placeholder="أدخل أرقام المجموعة (رقم في كل سطر)"></textarea>
                    </div>
                    <button class="btn" onclick="saveGroup()">💾 حفظ المجموعة</button>
                </div>

                <div class="card">
                    <h3>المجموعات المحفوظة</h3>
                    <div id="groupsList" class="number-list">
                        <p style="text-align: center; color: #666;">لا توجد مجموعات محفوظة</p>
                    </div>
                    <div style="margin-top: 15px;">
                        <button class="btn btn-secondary" onclick="refreshGroups()">🔄 تحديث</button>
                        <button class="btn btn-danger" onclick="clearAllGroups()">🗑️ مسح الكل</button>
                    </div>
                </div>
            </div>
            <div id="groupsAlert" class="alert"></div>
        </div>
    </div>

    <!-- QR Code Generator - Local Implementation -->
    <script>
        // Local QR Code generator using Google Charts API (no external library needed)
        const QRCodeLocal = {
            toCanvas: function(canvas, text, options, callback) {
                console.log('🔄 استخدام مولد QR Code المحلي...');

                try {
                    // Create QR Code using Google Charts API
                    const size = options.width || 300;
                    const qrUrl = `https://chart.googleapis.com/chart?chs=${size}x${size}&cht=qr&chl=${encodeURIComponent(text)}&choe=UTF-8`;

                    // Create image element
                    const img = new Image();
                    img.crossOrigin = 'anonymous';

                    img.onload = function() {
                        // Draw image on canvas
                        const ctx = canvas.getContext('2d');
                        canvas.width = size;
                        canvas.height = size;
                        ctx.drawImage(img, 0, 0, size, size);

                        console.log('✅ تم إنشاء QR Code باستخدام Google Charts');
                        callback(null);
                    };

                    img.onerror = function() {
                        console.log('❌ فشل Google Charts، استخدام QR Server...');

                        // Fallback to QR Server
                        const qrUrl2 = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(text)}`;

                        const img2 = new Image();
                        img2.crossOrigin = 'anonymous';

                        img2.onload = function() {
                            const ctx = canvas.getContext('2d');
                            canvas.width = size;
                            canvas.height = size;
                            ctx.drawImage(img2, 0, 0, size, size);

                            console.log('✅ تم إنشاء QR Code باستخدام QR Server');
                            callback(null);
                        };

                        img2.onerror = function() {
                            console.log('❌ فشل جميع الخدمات، استخدام QR Code نصي...');

                            // Final fallback: Text-based QR code
                            const ctx = canvas.getContext('2d');
                            canvas.width = size;
                            canvas.height = size;

                            // Draw background
                            ctx.fillStyle = '#FFFFFF';
                            ctx.fillRect(0, 0, size, size);

                            // Draw border
                            ctx.strokeStyle = '#075E54';
                            ctx.lineWidth = 4;
                            ctx.strokeRect(0, 0, size, size);

                            // Draw text
                            ctx.fillStyle = '#075E54';
                            ctx.font = 'bold 16px Arial';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';

                            // Split text into lines
                            const maxWidth = size - 40;
                            const lines = wrapText(ctx, text, maxWidth);
                            const lineHeight = 20;
                            const startY = (size - (lines.length * lineHeight)) / 2;

                            lines.forEach((line, index) => {
                                ctx.fillText(line, size / 2, startY + (index * lineHeight));
                            });

                            // Add QR-like pattern
                            drawQRPattern(ctx, size);

                            console.log('✅ تم إنشاء QR Code نصي كبديل');
                            callback(null);
                        };

                        img2.src = qrUrl2;
                    };

                    img.src = qrUrl;

                } catch (error) {
                    console.error('❌ خطأ في مولد QR Code المحلي:', error);
                    callback(error);
                }
            }
        };

        // Helper function to wrap text
        function wrapText(ctx, text, maxWidth) {
            const words = text.split(' ');
            const lines = [];
            let currentLine = words[0];

            for (let i = 1; i < words.length; i++) {
                const word = words[i];
                const width = ctx.measureText(currentLine + ' ' + word).width;
                if (width < maxWidth) {
                    currentLine += ' ' + word;
                } else {
                    lines.push(currentLine);
                    currentLine = word;
                }
            }
            lines.push(currentLine);
            return lines;
        }

        // Helper function to draw QR-like pattern
        function drawQRPattern(ctx, size) {
            const blockSize = 8;
            const blocks = Math.floor(size / blockSize);

            ctx.fillStyle = '#075E54';

            // Draw corner squares
            for (let i = 0; i < 7; i++) {
                for (let j = 0; j < 7; j++) {
                    if ((i === 0 || i === 6 || j === 0 || j === 6) || (i >= 2 && i <= 4 && j >= 2 && j <= 4)) {
                        ctx.fillRect(i * blockSize, j * blockSize, blockSize - 1, blockSize - 1);
                        ctx.fillRect((blocks - 7 + i) * blockSize, j * blockSize, blockSize - 1, blockSize - 1);
                        ctx.fillRect(i * blockSize, (blocks - 7 + j) * blockSize, blockSize - 1, blockSize - 1);
                    }
                }
            }

            // Draw random pattern
            for (let i = 8; i < blocks - 8; i++) {
                for (let j = 8; j < blocks - 8; j++) {
                    if (Math.random() > 0.5) {
                        ctx.fillRect(i * blockSize, j * blockSize, blockSize - 1, blockSize - 1);
                    }
                }
            }
        }

        // Set global QRCode to use local implementation
        window.QRCode = QRCodeLocal;
        console.log('✅ مولد QR Code المحلي جاهز');
    </script>

    <script>
        // Global variables
        let settings = {
            instanceId: '',
            apiToken: '',
            connectionMethod: 'api' // 'api' or 'phone'
        };
        let phoneConnection = {
            isConnected: false,
            sessionId: null,
            qrCode: null
        };
        let groups = {};
        let messageTemplates = {
            welcome: 'مرحباً بك! نحن سعداء لانضمامك إلينا. إذا كان لديك أي استفسار، لا تتردد في التواصل معنا.',
            offer: '🎉 عرض خاص لفترة محدودة! احصل على خصم 30% على جميع خدماتنا. العرض ساري حتى نهاية الشهر.',
            reminder: 'تذكير ودي: لديك موعد معنا غداً. يرجى التأكيد أو إعلامنا في حالة الحاجة لإعادة الجدولة.',
            thanks: 'شكراً لك على ثقتك بنا! نقدر تعاملك معنا ونتطلع لخدمتك مرة أخرى.'
        };

        // Load settings on page load
        window.onload = function() {
            loadSettings();
            loadGroups();
            updateConnectionMethodUI();

            // Test QR Code library after page load
            setTimeout(testQRCodeLibrary, 1000);
        };

        // Test QR Code library
        function testQRCodeLibrary() {
            console.log('🔍 اختبار مولد QR Code...');

            if (typeof QRCode === 'undefined') {
                console.error('❌ مولد QR Code غير متوفر');
                showAlert('apiAlert', '⚠️ تحذير: مولد QR Code غير متوفر. قد لا تعمل ميزة ربط الهاتف.', 'error');
            } else {
                console.log('✅ مولد QR Code جاهز (محلي)');
                console.log('📋 نوع المولد: محلي مع خدمات بديلة');
                showAlert('apiAlert', '✅ مولد QR Code المحلي جاهز للاستخدام!', 'success');
            }
        }

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Connection Method functions
        function switchConnectionMethod(method) {
            settings.connectionMethod = method;
            updateConnectionMethodUI();
            saveSettings();
        }

        function updateConnectionMethodUI() {
            const apiSettings = document.getElementById('apiSettings');
            const phoneSettings = document.getElementById('phoneSettings');

            if (settings.connectionMethod === 'api') {
                apiSettings.style.display = 'grid';
                phoneSettings.style.display = 'none';
            } else {
                apiSettings.style.display = 'none';
                phoneSettings.style.display = 'grid';
            }

            // Update radio buttons
            document.querySelector(`input[name="connectionMethod"][value="${settings.connectionMethod}"]`).checked = true;

            // Update connection method styles
            document.querySelectorAll('label').forEach(label => {
                const radio = label.querySelector('input[type="radio"]');
                if (radio && radio.name === 'connectionMethod') {
                    if (radio.checked) {
                        label.style.borderColor = '#25D366';
                        label.style.background = 'rgba(37, 211, 102, 0.1)';
                    } else {
                        label.style.borderColor = '#ddd';
                        label.style.background = 'transparent';
                    }
                }
            });
        }

        // Phone Connection functions
        function connectPhone() {
            console.log('📱 بدء ربط الهاتف...');

            // Update status to connecting
            updatePhoneConnectionStatus('connecting', '🔄', 'جاري التحضير...', 'يتم فتح WhatsApp Web للربط');

            // Generate a session ID
            phoneConnection.sessionId = 'session_' + Date.now();

            // Show WhatsApp Web container
            document.getElementById('phoneQRPlaceholder').style.display = 'none';
            document.getElementById('whatsappWebContainer').style.display = 'block';
            document.getElementById('phoneQRInstructions').style.display = 'block';

            // Show confirm button
            document.getElementById('confirmConnectionBtn').style.display = 'inline-block';

            // Update status
            updatePhoneConnectionStatus('connecting', '🌐', 'جاهز للربط', 'اضغط "فتح WhatsApp Web" واتبع التعليمات');

            showAlert('apiAlert', '📱 جاهز للربط! اضغط "فتح WhatsApp Web" واتبع التعليمات.', 'info');
        }

        function openWhatsAppWeb() {
            console.log('🌐 فتح WhatsApp Web...');

            // Open WhatsApp Web in new window
            const whatsappWebUrl = 'https://web.whatsapp.com';
            window.open(whatsappWebUrl, '_blank', 'width=1200,height=800');

            updatePhoneConnectionStatus('connecting', '🌐', 'WhatsApp Web مفتوح', 'امسح QR Code في النافذة الجديدة ثم اضغط "تأكيد الربط"');

            showAlert('apiAlert', '🌐 تم فتح WhatsApp Web! امسح QR Code بهاتفك ثم اضغط "تأكيد الربط".', 'info');
        }

        function copyWhatsAppWebLink() {
            const whatsappWebUrl = 'https://web.whatsapp.com';

            navigator.clipboard.writeText(whatsappWebUrl).then(() => {
                showAlert('apiAlert', '📋 تم نسخ رابط WhatsApp Web! الصقه في متصفح جديد.', 'success');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = whatsappWebUrl;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('apiAlert', '📋 تم نسخ رابط WhatsApp Web!', 'success');
            });
        }

        function confirmConnection() {
            console.log('✅ تأكيد ربط الهاتف...');

            // Simulate connection confirmation
            updatePhoneConnectionStatus('connected', '✅', 'متصل بنجاح', 'هاتفك مرتبط عبر WhatsApp Web ويمكنك الإرسال الآن');

            phoneConnection.isConnected = true;

            // Update buttons
            document.getElementById('connectPhoneBtn').style.display = 'none';
            document.getElementById('confirmConnectionBtn').style.display = 'none';
            document.getElementById('disconnectPhoneBtn').style.display = 'inline-block';

            showAlert('apiAlert', '✅ تم تأكيد ربط الهاتف بنجاح! يمكنك الآن إرسال الرسائل.', 'success');
        }

        function disconnectPhone() {
            console.log('🔌 قطع اتصال الهاتف...');

            phoneConnection.isConnected = false;
            phoneConnection.sessionId = null;

            updatePhoneConnectionStatus('disconnected', '📱', 'غير متصل', 'اضغط "ربط الهاتف" لبدء الاتصال');

            // Reset buttons
            document.getElementById('connectPhoneBtn').style.display = 'inline-block';
            document.getElementById('confirmConnectionBtn').style.display = 'none';
            document.getElementById('disconnectPhoneBtn').style.display = 'none';

            // Hide WhatsApp Web container
            document.getElementById('phoneQRPlaceholder').style.display = 'block';
            document.getElementById('whatsappWebContainer').style.display = 'none';
            document.getElementById('phoneQRInstructions').style.display = 'none';

            showAlert('apiAlert', 'تم قطع الاتصال مع الهاتف.', 'info');
        }

        function regenerateQR() {
            console.log('🔄 إعادة إنشاء QR Code...');

            // Generate new session ID
            phoneConnection.sessionId = 'session_' + Date.now();

            // Update status
            updatePhoneConnectionStatus('connecting', '🔄', 'جاري إنشاء QR Code جديد...', 'يتم إنشاء كود جديد للربط');

            // Hide regenerate button temporarily
            document.getElementById('regenerateQRBtn').style.display = 'none';

            // Generate new QR Code
            generatePhoneQR();
        }

        function updatePhoneConnectionStatus(status, icon, text, description) {
            const statusContainer = document.getElementById('phoneConnectionStatus').querySelector('div');
            const iconElement = document.getElementById('connectionStatusIcon');
            const textElement = document.getElementById('connectionStatusText');
            const descElement = document.getElementById('connectionStatusDesc');

            // Update content
            iconElement.textContent = icon;
            textElement.textContent = text;
            descElement.textContent = description;

            // Update styles
            statusContainer.className = `connection-status ${status}`;

            // Update colors
            if (status === 'connected') {
                textElement.style.color = '#28a745';
            } else if (status === 'connecting') {
                textElement.style.color = '#ffc107';
            } else {
                textElement.style.color = '#666';
            }
        }

        function generatePhoneQR() {
            console.log('🔄 بدء إنشاء QR Code للربط...');

            const canvas = document.getElementById('phoneQRCanvas');
            const placeholder = document.getElementById('phoneQRPlaceholder');
            const instructions = document.getElementById('phoneQRInstructions');

            // التحقق من وجود العناصر
            if (!canvas) {
                console.error('❌ عنصر Canvas غير موجود');
                showAlert('apiAlert', 'خطأ: عنصر Canvas غير موجود', 'error');
                return;
            }

            if (!placeholder) {
                console.error('❌ عنصر Placeholder غير موجود');
                showAlert('apiAlert', 'خطأ: عنصر Placeholder غير موجود', 'error');
                return;
            }

            // إخفاء placeholder فوراً
            placeholder.style.display = 'none';

            // إظهار رسالة التحميل
            const container = canvas.parentNode;
            let loadingDiv = document.getElementById('qrLoading');
            if (!loadingDiv) {
                loadingDiv = document.createElement('div');
                loadingDiv.id = 'qrLoading';
                loadingDiv.style.cssText = 'text-align: center; padding: 40px; color: #666; border: 2px dashed #25D366; border-radius: 10px; background: rgba(37, 211, 102, 0.1);';
                loadingDiv.innerHTML = '<div style="font-size: 2rem; animation: spin 1s linear infinite;">⏳</div><p><strong>جاري إنشاء QR Code...</strong></p>';
                container.insertBefore(loadingDiv, canvas);
            }
            loadingDiv.style.display = 'block';

            // التحقق من توفر مولد QR Code
            if (typeof QRCode === 'undefined') {
                console.error('❌ مولد QR Code غير متوفر');
                loadingDiv.innerHTML = '<div style="font-size: 2rem; color: #dc3545;">❌</div><p><strong>خطأ: مولد QR Code غير متوفر</strong></p><p>أعد تحميل الصفحة وحاول مرة أخرى</p>';
                showAlert('apiAlert', 'خطأ: مولد QR Code غير متوفر. أعد تحميل الصفحة.', 'error');
                document.getElementById('regenerateQRBtn').style.display = 'inline-block';
                return;
            }

            // إنشاء بيانات QR Code
            const timestamp = Date.now();
            const randomId = Math.random().toString(36).substring(2, 15);
            const qrData = `https://wa.me/qr/DEMO${randomId}${timestamp}`;

            console.log('📱 إنشاء QR Code بالبيانات:', qrData);

            // محاولة إنشاء QR Code
            try {
                QRCode.toCanvas(canvas, qrData, {
                    width: 300,
                    height: 300,
                    margin: 2,
                    color: {
                        dark: '#075E54',
                        light: '#FFFFFF'
                    },
                    errorCorrectionLevel: 'M'
                }, function (error) {
                    // إزالة رسالة التحميل
                    if (loadingDiv) {
                        loadingDiv.style.display = 'none';
                    }

                    if (error) {
                        console.error('❌ خطأ في إنشاء QR Code:', error);
                        placeholder.style.display = 'block';
                        placeholder.innerHTML = '<div style="color: #dc3545; font-size: 2rem;">❌</div><p><strong>فشل إنشاء QR Code</strong></p><p>اضغط "إعادة إنشاء QR Code" للمحاولة مرة أخرى</p>';
                        canvas.style.display = 'none';
                        showAlert('apiAlert', 'فشل في إنشاء QR Code: ' + error.message, 'error');
                        document.getElementById('regenerateQRBtn').style.display = 'inline-block';
                    } else {
                        console.log('✅ تم إنشاء QR Code بنجاح');

                        // إظهار QR Code
                        canvas.style.display = 'block';
                        if (instructions) {
                            instructions.style.display = 'block';
                        }

                        // إضافة تأثير بصري
                        canvas.style.border = '3px solid #25D366';
                        canvas.style.borderRadius = '10px';
                        canvas.style.boxShadow = '0 0 20px rgba(37, 211, 102, 0.3)';

                        // تحديث الحالة
                        updatePhoneConnectionStatus('connecting', '📱', 'امسح QR Code', 'استخدم كاميرا هاتفك لمسح الكود أعلاه');

                        // إظهار زر إعادة الإنشاء
                        document.getElementById('regenerateQRBtn').style.display = 'inline-block';

                        showAlert('apiAlert', '✅ تم إنشاء QR Code بنجاح! امسحه بهاتفك الآن.', 'success');
                    }
                });
            } catch (e) {
                console.error('❌ استثناء في إنشاء QR Code:', e);
                if (loadingDiv) {
                    loadingDiv.style.display = 'none';
                }
                placeholder.style.display = 'block';
                placeholder.innerHTML = '<div style="color: #dc3545; font-size: 2rem;">❌</div><p><strong>خطأ في النظام</strong></p><p>أعد تحميل الصفحة وحاول مرة أخرى</p>';
                showAlert('apiAlert', 'خطأ في النظام: ' + e.message, 'error');
                document.getElementById('regenerateQRBtn').style.display = 'inline-block';
            }
        }

        // Settings functions
        function saveSettings() {
            if (settings.connectionMethod === 'api') {
                settings.instanceId = document.getElementById('instanceId').value.trim();
                settings.apiToken = document.getElementById('apiToken').value.trim();

                if (!settings.instanceId || !settings.apiToken) {
                    showAlert('apiAlert', 'يرجى إدخال Instance ID و API Token', 'error');
                    return;
                }
            }

            localStorage.setItem('whatsappSettings', JSON.stringify(settings));
            showAlert('apiAlert', 'تم حفظ الإعدادات بنجاح!', 'success');
        }

        function loadSettings() {
            const saved = localStorage.getItem('whatsappSettings');
            if (saved) {
                settings = JSON.parse(saved);
                document.getElementById('instanceId').value = settings.instanceId;
                document.getElementById('apiToken').value = settings.apiToken;
            }
        }

        function testConnection() {
            if (!settings.instanceId || !settings.apiToken) {
                showAlert('apiAlert', 'يرجى حفظ الإعدادات أولاً', 'error');
                return;
            }

            const url = `https://api.green-api.com/waInstance${settings.instanceId}/getSettings/${settings.apiToken}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log('📋 استجابة API:', data);
                    if (data.stateInstance === 'authorized') {
                        showAlert('apiAlert', '✅ تم الاتصال بنجاح! WhatsApp مرتبط وجاهز للإرسال.', 'success');
                    } else if (data.stateInstance === 'notAuthorized') {
                        showAlert('apiAlert', '⚠️ API متصل لكن WhatsApp غير مرتبط. امسح QR Code في لوحة Green API.', 'error');
                    } else {
                        showAlert('apiAlert', `⚠️ حالة الاتصال: ${data.stateInstance}. تحقق من ربط WhatsApp.`, 'error');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في الاتصال:', error);
                    showAlert('apiAlert', '❌ خطأ في الاتصال: ' + error.message, 'error');
                });
        }

        // Message functions
        function sendMessage(phone, message) {
            return new Promise((resolve, reject) => {
                if (settings.connectionMethod === 'api') {
                    // Send via Green API
                    if (!settings.instanceId || !settings.apiToken) {
                        reject('لم يتم تكوين إعدادات API');
                        return;
                    }

                    const url = `https://api.green-api.com/waInstance${settings.instanceId}/sendMessage/${settings.apiToken}`;

                    // Format phone number correctly
                    let chatId = phone.replace(/\D/g, ''); // Remove non-digits
                    if (!chatId.endsWith('@c.us')) {
                        chatId = chatId + '@c.us';
                    }

                    console.log('📤 إرسال رسالة عبر API...');
                    console.log('📱 الرقم:', chatId);
                    console.log('💬 الرسالة:', message);

                    fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            chatId: chatId,
                            message: message
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('📋 استجابة الإرسال:', data);

                        if (data.idMessage) {
                            resolve(`✅ تم الإرسال بنجاح عبر API (ID: ${data.idMessage})`);
                        } else if (data.error) {
                            reject(`❌ خطأ من API: ${data.error}`);
                        } else {
                            reject('❌ استجابة غير متوقعة من API');
                        }
                    })
                    .catch(error => {
                        console.error('❌ خطأ في الإرسال:', error);
                        reject('خطأ في الإرسال عبر API: ' + error.message);
                    });
                } else {
                    // Send via Phone Connection (WhatsApp Web)
                    if (!phoneConnection.isConnected) {
                        reject('الهاتف غير مرتبط. يرجى ربط الهاتف أولاً من تبويب الإعدادات.');
                        return;
                    }

                    console.log('📱 إرسال عبر WhatsApp Web...');
                    console.log('📱 الرقم:', phone);
                    console.log('💬 الرسالة:', message);

                    // Create WhatsApp Web URL
                    const cleanPhone = phone.replace(/\D/g, ''); // Remove non-digits
                    const whatsappUrl = `https://web.whatsapp.com/send?phone=${cleanPhone}&text=${encodeURIComponent(message)}`;

                    // Open WhatsApp Web with pre-filled message
                    window.open(whatsappUrl, '_blank');

                    // Simulate successful sending (user will manually send)
                    setTimeout(() => {
                        resolve('✅ تم فتح WhatsApp Web مع الرسالة جاهزة. اضغط إرسال في WhatsApp Web.');
                    }, 1000);
                }
            });
        }

        function sendSingleMessage() {
            const phone = document.getElementById('singlePhone').value.trim();
            const message = document.getElementById('singleMessage').value.trim();

            if (!phone || !message) {
                showAlert('singleAlert', 'يرجى إدخال رقم الهاتف والرسالة', 'error');
                return;
            }

            sendMessage(phone, message)
                .then(result => {
                    showAlert('singleAlert', '✅ ' + result, 'success');
                    document.getElementById('singlePhone').value = '';
                    document.getElementById('singleMessage').value = '';
                })
                .catch(error => {
                    showAlert('singleAlert', '❌ ' + error, 'error');
                });
        }

        function sendBulkMessages() {
            const numbersText = document.getElementById('bulkNumbers').value.trim();
            const message = document.getElementById('bulkMessage').value.trim();
            const delay = parseInt(document.getElementById('delaySeconds').value) * 1000;

            if (!numbersText || !message) {
                showAlert('bulkAlert', 'يرجى إدخال الأرقام والرسالة', 'error');
                return;
            }

            const numbers = numbersText.split('\n')
                .map(line => line.trim())
                .filter(line => line && /^\d+$/.test(line));

            if (numbers.length === 0) {
                showAlert('bulkAlert', 'لم يتم العثور على أرقام صحيحة', 'error');
                return;
            }

            if (!confirm(`هل تريد إرسال الرسالة إلى ${numbers.length} رقم؟`)) {
                return;
            }

            // Show progress
            document.getElementById('bulkProgress').style.display = 'block';
            document.getElementById('bulkStatus').textContent = 'جاري الإرسال...';

            let successCount = 0;
            let failedCount = 0;

            async function sendToNumbers() {
                for (let i = 0; i < numbers.length; i++) {
                    try {
                        await sendMessage(numbers[i], message);
                        successCount++;
                        updateProgress(i + 1, numbers.length, `✅ تم إرسال ${i + 1}/${numbers.length}`);
                    } catch (error) {
                        failedCount++;
                        updateProgress(i + 1, numbers.length, `❌ فشل ${i + 1}/${numbers.length}`);
                    }

                    if (i < numbers.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }

                // Final results
                document.getElementById('bulkProgress').style.display = 'none';
                showAlert('bulkAlert', `تم الانتهاء! ناجح: ${successCount}, فاشل: ${failedCount}`, 'info');
            }

            sendToNumbers();
        }

        function updateProgress(current, total, status) {
            const percentage = (current / total) * 100;
            document.getElementById('bulkProgressBar').style.width = percentage + '%';
            document.getElementById('bulkStatus').textContent = status;
        }

        // Template functions
        function loadTemplate() {
            const templateName = document.getElementById('messageTemplates').value;
            if (templateName && messageTemplates[templateName]) {
                document.getElementById('singleMessage').value = messageTemplates[templateName];
            }
        }

        function previewMessage() {
            const message = document.getElementById('singleMessage').value;
            if (message) {
                alert('معاينة الرسالة:\n\n' + message);
            } else {
                alert('لا توجد رسالة للمعاينة');
            }
        }

        function clearMessage() {
            document.getElementById('singleMessage').value = '';
            document.getElementById('messageTemplates').value = '';
        }

        // Groups functions
        function saveGroup() {
            const groupName = document.getElementById('groupName').value.trim();
            const numbersText = document.getElementById('groupNumbers').value.trim();

            if (!groupName || !numbersText) {
                showAlert('groupsAlert', 'يرجى إدخال اسم المجموعة والأرقام', 'error');
                return;
            }

            const numbers = numbersText.split('\n')
                .map(line => line.trim())
                .filter(line => line && /^\d+$/.test(line));

            if (numbers.length === 0) {
                showAlert('groupsAlert', 'لم يتم العثور على أرقام صحيحة', 'error');
                return;
            }

            groups[groupName] = numbers;
            localStorage.setItem('whatsappGroups', JSON.stringify(groups));

            showAlert('groupsAlert', `تم حفظ المجموعة "${groupName}" بنجاح! (${numbers.length} رقم)`, 'success');

            document.getElementById('groupName').value = '';
            document.getElementById('groupNumbers').value = '';
            refreshGroups();
        }

        function loadGroups() {
            const saved = localStorage.getItem('whatsappGroups');
            if (saved) {
                groups = JSON.parse(saved);
                refreshGroups();
            }
        }

        function refreshGroups() {
            const groupsList = document.getElementById('groupsList');

            if (Object.keys(groups).length === 0) {
                groupsList.innerHTML = '<p style="text-align: center; color: #666;">لا توجد مجموعات محفوظة</p>';
                return;
            }

            let html = '';
            for (const [name, numbers] of Object.entries(groups)) {
                html += `
                    <div class="number-item">
                        <strong>${name}</strong> (${numbers.length} رقم)
                        <button class="btn btn-secondary" style="float: left; padding: 5px 10px; font-size: 0.8rem;" onclick="loadGroupToNumbers('${name}')">تحميل</button>
                        <button class="btn btn-danger" style="float: left; padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="deleteGroup('${name}')">حذف</button>
                    </div>
                `;
            }
            groupsList.innerHTML = html;
        }

        function loadGroupToNumbers(groupName) {
            if (groups[groupName]) {
                document.getElementById('bulkNumbers').value = groups[groupName].join('\n');
                showTab('bulk');
                showAlert('bulkAlert', `تم تحميل مجموعة "${groupName}" (${groups[groupName].length} رقم)`, 'success');
            }
        }

        function deleteGroup(groupName) {
            if (confirm(`هل تريد حذف المجموعة "${groupName}"؟`)) {
                delete groups[groupName];
                localStorage.setItem('whatsappGroups', JSON.stringify(groups));
                refreshGroups();
                showAlert('groupsAlert', `تم حذف المجموعة "${groupName}"`, 'info');
            }
        }

        function clearAllGroups() {
            if (confirm('هل تريد حذف جميع المجموعات؟')) {
                groups = {};
                localStorage.setItem('whatsappGroups', JSON.stringify(groups));
                refreshGroups();
                showAlert('groupsAlert', 'تم حذف جميع المجموعات', 'info');
            }
        }

        function loadFromGroup() {
            if (Object.keys(groups).length === 0) {
                showAlert('bulkAlert', 'لا توجد مجموعات محفوظة', 'error');
                return;
            }

            let groupOptions = '';
            for (const name of Object.keys(groups)) {
                groupOptions += `<option value="${name}">${name} (${groups[name].length} رقم)</option>`;
            }

            const selectedGroup = prompt(`اختر المجموعة:\n\n${Object.keys(groups).map((name, index) => `${index + 1}. ${name} (${groups[name].length} رقم)`).join('\n')}\n\nأدخل اسم المجموعة:`);

            if (selectedGroup && groups[selectedGroup]) {
                loadGroupToNumbers(selectedGroup);
            }
        }

        function loadNumbersFromFile() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.txt,.csv';
            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const content = e.target.result;
                        const numbers = content.split('\n')
                            .map(line => line.trim())
                            .filter(line => line && !line.startsWith('#'))
                            .map(line => line.replace(/\D/g, ''))
                            .filter(line => line.length >= 10);

                        document.getElementById('bulkNumbers').value = numbers.join('\n');
                        showAlert('bulkAlert', `تم تحميل ${numbers.length} رقم من الملف`, 'success');
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // QR Code functions
        function generateQR() {
            const phone = document.getElementById('qrPhone').value.trim();
            const message = document.getElementById('qrMessage').value.trim();
            const website = document.getElementById('qrWebsite').value.trim();

            if (!phone || !message) {
                document.getElementById('qrPlaceholder').style.display = 'block';
                document.getElementById('qrCanvas').style.display = 'none';
                document.getElementById('downloadBtn').style.display = 'none';
                document.getElementById('printBtn').style.display = 'none';
                document.getElementById('quickLinks').style.display = 'none';
                return;
            }

            // Build message with website if provided
            let fullMessage = message;
            if (website) {
                fullMessage += `\n\n🌐 الموقع: ${website}`;
            }

            // Create WhatsApp URL
            const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(fullMessage)}`;

            // Generate QR Code
            const canvas = document.getElementById('qrCanvas');
            QRCode.toCanvas(canvas, whatsappUrl, {
                width: 300,
                margin: 2,
                color: {
                    dark: '#075E54',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) {
                    console.error(error);
                    showAlert('qrAlert', 'خطأ في إنشاء QR Code', 'error');
                } else {
                    document.getElementById('qrPlaceholder').style.display = 'none';
                    document.getElementById('qrCanvas').style.display = 'block';
                    document.getElementById('downloadBtn').style.display = 'inline-block';
                    document.getElementById('printBtn').style.display = 'inline-block';

                    // Update links
                    document.getElementById('whatsappLink').value = whatsappUrl;
                    document.getElementById('shortLink').value = createShortLink(whatsappUrl);
                    document.getElementById('quickLinks').style.display = 'block';
                }
            });
        }

        function createShortLink(url) {
            // Create a shorter, more readable link
            const phone = document.getElementById('qrPhone').value.trim();
            const message = document.getElementById('qrMessage').value.trim().substring(0, 50);
            return `wa.me/${phone}?text=${encodeURIComponent(message)}...`;
        }

        function sendViaQR() {
            const phone = document.getElementById('qrPhone').value.trim();
            const message = document.getElementById('qrMessage').value.trim();

            if (!phone || !message) {
                showAlert('qrAlert', 'يرجى إدخال رقم الهاتف والرسالة', 'error');
                return;
            }

            const website = document.getElementById('qrWebsite').value.trim();
            let fullMessage = message;
            if (website) {
                fullMessage += `\n\n🌐 الموقع: ${website}`;
            }

            const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(fullMessage)}`;
            window.open(whatsappUrl, '_blank');
            showAlert('qrAlert', 'تم فتح WhatsApp في نافذة جديدة', 'success');
        }

        function copyQRLink() {
            const phone = document.getElementById('qrPhone').value.trim();
            const message = document.getElementById('qrMessage').value.trim();

            if (!phone || !message) {
                showAlert('qrAlert', 'يرجى إدخال رقم الهاتف والرسالة أولاً', 'error');
                return;
            }

            const website = document.getElementById('qrWebsite').value.trim();
            let fullMessage = message;
            if (website) {
                fullMessage += `\n\n🌐 الموقع: ${website}`;
            }

            const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(fullMessage)}`;

            navigator.clipboard.writeText(whatsappUrl).then(() => {
                showAlert('qrAlert', 'تم نسخ الرابط بنجاح!', 'success');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = whatsappUrl;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('qrAlert', 'تم نسخ الرابط بنجاح!', 'success');
            });
        }

        function downloadQR() {
            const canvas = document.getElementById('qrCanvas');
            const link = document.createElement('a');
            link.download = 'whatsapp-qr-code.png';
            link.href = canvas.toDataURL();
            link.click();
            showAlert('qrAlert', 'تم تحميل QR Code بنجاح!', 'success');
        }

        function printQR() {
            const canvas = document.getElementById('qrCanvas');
            const phone = document.getElementById('qrPhone').value.trim();
            const message = document.getElementById('qrMessage').value.trim();

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>WhatsApp QR Code</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            text-align: center;
                            padding: 20px;
                        }
                        .qr-container {
                            border: 2px solid #075E54;
                            padding: 20px;
                            border-radius: 10px;
                            display: inline-block;
                        }
                        .header {
                            color: #075E54;
                            margin-bottom: 20px;
                        }
                        .instructions {
                            margin-top: 20px;
                            font-size: 14px;
                            color: #666;
                        }
                    </style>
                </head>
                <body>
                    <div class="qr-container">
                        <h2 class="header">🚀 WhatsApp Sender Pro</h2>
                        <img src="${canvas.toDataURL()}" alt="QR Code">
                        <div class="instructions">
                            <p><strong>رقم الهاتف:</strong> ${phone}</p>
                            <p><strong>الرسالة:</strong> ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}</p>
                            <br>
                            <p>امسح هذا الكود بكاميرا هاتفك لإرسال الرسالة عبر WhatsApp</p>
                        </div>
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function copyLink(linkId) {
            const linkElement = document.getElementById(linkId);
            linkElement.select();
            document.execCommand('copy');
            showAlert('qrAlert', 'تم نسخ الرابط بنجاح!', 'success');
        }

        function openLink(linkId) {
            const link = document.getElementById(linkId).value;
            if (link) {
                window.open(link, '_blank');
                showAlert('qrAlert', 'تم فتح الرابط في نافذة جديدة', 'success');
            }
        }

        function shareLink() {
            const link = document.getElementById('whatsappLink').value;
            if (navigator.share) {
                navigator.share({
                    title: 'WhatsApp Message Link',
                    text: 'رابط لإرسال رسالة WhatsApp',
                    url: link
                });
            } else {
                copyLink('whatsappLink');
            }
        }

        // Utility functions
        function showAlert(elementId, message, type) {
            const alert = document.getElementById(elementId);
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';

            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
