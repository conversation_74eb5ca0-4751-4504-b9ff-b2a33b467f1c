<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sender Pro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #075E54 0%, #128C7E 50%, #25D366 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #075E54, #128C7E);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab.active {
            background: white;
            border-bottom-color: #25D366;
            color: #075E54;
            font-weight: bold;
        }

        .tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #25D366;
            box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .card h3 {
            color: #075E54;
            margin-bottom: 15px;
        }

        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #25D366, #128C7E);
            width: 0%;
            transition: width 0.3s ease;
        }

        .number-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }

        .number-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }

        .number-item:last-child {
            border-bottom: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }

        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WhatsApp Sender Pro</h1>
            <p>أداة احترافية لإرسال رسائل WhatsApp بسهولة وفعالية</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('api')">⚙️ إعدادات API</button>
            <button class="tab" onclick="showTab('single')">📱 رسالة واحدة</button>
            <button class="tab" onclick="showTab('bulk')">📊 رسائل جماعية</button>
            <button class="tab" onclick="showTab('groups')">👥 مجموعات الأرقام</button>
        </div>

        <!-- API Settings Tab -->
        <div id="api" class="tab-content active">
            <div class="grid">
                <div class="card">
                    <h3>إعدادات Green API</h3>
                    <div class="form-group">
                        <label>Instance ID:</label>
                        <input type="text" id="instanceId" placeholder="أدخل Instance ID">
                    </div>
                    <div class="form-group">
                        <label>API Token:</label>
                        <input type="password" id="apiToken" placeholder="أدخل API Token">
                    </div>
                    <button class="btn" onclick="saveSettings()">💾 حفظ الإعدادات</button>
                    <button class="btn btn-secondary" onclick="testConnection()">🔍 اختبار الاتصال</button>
                </div>
                
                <div class="card">
                    <h3>كيفية الحصول على بيانات API</h3>
                    <ol style="line-height: 1.8;">
                        <li>اذهب إلى: <a href="https://green-api.com" target="_blank">green-api.com</a></li>
                        <li>سجل حساب مجاني</li>
                        <li>أنشئ Instance جديد</li>
                        <li>انسخ Instance ID و API Token</li>
                        <li>الصقهما في الحقول أعلاه</li>
                        <li>امسح QR Code لربط WhatsApp</li>
                    </ol>
                </div>
            </div>
            <div id="apiAlert" class="alert"></div>
        </div>

        <!-- Single Message Tab -->
        <div id="single" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>إرسال رسالة واحدة</h3>
                    <div class="form-group">
                        <label>رقم الهاتف (مع كود الدولة):</label>
                        <input type="text" id="singlePhone" placeholder="مثال: 201234567890">
                        <small>أدخل الرقم بدون + أو 00</small>
                    </div>
                    <div class="form-group">
                        <label>الرسالة:</label>
                        <textarea id="singleMessage" rows="5" placeholder="اكتب رسالتك هنا..."></textarea>
                    </div>
                    <button class="btn" onclick="sendSingleMessage()">📤 إرسال الرسالة</button>
                </div>
                
                <div class="card">
                    <h3>قوالب الرسائل</h3>
                    <select id="messageTemplates" onchange="loadTemplate()">
                        <option value="">اختر قالب...</option>
                        <option value="welcome">رسالة ترحيب</option>
                        <option value="offer">عرض خاص</option>
                        <option value="reminder">تذكير</option>
                        <option value="thanks">شكر</option>
                    </select>
                    <div style="margin-top: 15px;">
                        <button class="btn btn-secondary" onclick="previewMessage()">👁️ معاينة</button>
                        <button class="btn btn-secondary" onclick="clearMessage()">🗑️ مسح</button>
                    </div>
                </div>
            </div>
            <div id="singleAlert" class="alert"></div>
        </div>

        <!-- Bulk Messages Tab -->
        <div id="bulk" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>أرقام الهواتف</h3>
                    <div class="form-group">
                        <label>أدخل الأرقام (رقم في كل سطر):</label>
                        <textarea id="bulkNumbers" rows="8" placeholder="201234567890&#10;201987654321&#10;201555666777"></textarea>
                    </div>
                    <button class="btn btn-secondary" onclick="loadNumbersFromFile()">📁 تحميل من ملف</button>
                    <button class="btn btn-secondary" onclick="loadFromGroup()">👥 تحميل من مجموعة</button>
                </div>
                
                <div class="card">
                    <h3>الرسالة والإعدادات</h3>
                    <div class="form-group">
                        <label>الرسالة:</label>
                        <textarea id="bulkMessage" rows="5" placeholder="اكتب رسالتك الجماعية هنا..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>التأخير بين الرسائل (ثواني):</label>
                        <input type="number" id="delaySeconds" value="3" min="1" max="60">
                    </div>
                    <button class="btn" onclick="sendBulkMessages()">📤 إرسال جماعي</button>
                    <div class="progress" id="bulkProgress">
                        <div class="progress-bar" id="bulkProgressBar"></div>
                    </div>
                    <div id="bulkStatus" style="margin-top: 10px; font-weight: bold;"></div>
                </div>
            </div>
            <div id="bulkAlert" class="alert"></div>
        </div>

        <!-- Number Groups Tab -->
        <div id="groups" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>إنشاء مجموعة جديدة</h3>
                    <div class="form-group">
                        <label>اسم المجموعة:</label>
                        <input type="text" id="groupName" placeholder="مثال: عملاء-VIP">
                    </div>
                    <div class="form-group">
                        <label>أرقام المجموعة:</label>
                        <textarea id="groupNumbers" rows="6" placeholder="أدخل أرقام المجموعة (رقم في كل سطر)"></textarea>
                    </div>
                    <button class="btn" onclick="saveGroup()">💾 حفظ المجموعة</button>
                </div>
                
                <div class="card">
                    <h3>المجموعات المحفوظة</h3>
                    <div id="groupsList" class="number-list">
                        <p style="text-align: center; color: #666;">لا توجد مجموعات محفوظة</p>
                    </div>
                    <div style="margin-top: 15px;">
                        <button class="btn btn-secondary" onclick="refreshGroups()">🔄 تحديث</button>
                        <button class="btn btn-danger" onclick="clearAllGroups()">🗑️ مسح الكل</button>
                    </div>
                </div>
            </div>
            <div id="groupsAlert" class="alert"></div>
        </div>
    </div>

    <script>
        // Global variables
        let settings = {
            instanceId: '',
            apiToken: ''
        };
        let groups = {};
        let messageTemplates = {
            welcome: 'مرحباً بك! نحن سعداء لانضمامك إلينا. إذا كان لديك أي استفسار، لا تتردد في التواصل معنا.',
            offer: '🎉 عرض خاص لفترة محدودة! احصل على خصم 30% على جميع خدماتنا. العرض ساري حتى نهاية الشهر.',
            reminder: 'تذكير ودي: لديك موعد معنا غداً. يرجى التأكيد أو إعلامنا في حالة الحاجة لإعادة الجدولة.',
            thanks: 'شكراً لك على ثقتك بنا! نقدر تعاملك معنا ونتطلع لخدمتك مرة أخرى.'
        };

        // Load settings on page load
        window.onload = function() {
            loadSettings();
            loadGroups();
        };

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Settings functions
        function saveSettings() {
            settings.instanceId = document.getElementById('instanceId').value.trim();
            settings.apiToken = document.getElementById('apiToken').value.trim();
            
            if (!settings.instanceId || !settings.apiToken) {
                showAlert('apiAlert', 'يرجى إدخال Instance ID و API Token', 'error');
                return;
            }

            localStorage.setItem('whatsappSettings', JSON.stringify(settings));
            showAlert('apiAlert', 'تم حفظ الإعدادات بنجاح!', 'success');
        }

        function loadSettings() {
            const saved = localStorage.getItem('whatsappSettings');
            if (saved) {
                settings = JSON.parse(saved);
                document.getElementById('instanceId').value = settings.instanceId;
                document.getElementById('apiToken').value = settings.apiToken;
            }
        }

        function testConnection() {
            if (!settings.instanceId || !settings.apiToken) {
                showAlert('apiAlert', 'يرجى حفظ الإعدادات أولاً', 'error');
                return;
            }

            const url = `https://api.green-api.com/waInstance${settings.instanceId}/getSettings/${settings.apiToken}`;
            
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        showAlert('apiAlert', '✅ تم الاتصال بنجاح! API يعمل بشكل صحيح.', 'success');
                    } else {
                        showAlert('apiAlert', '❌ فشل الاتصال! تحقق من البيانات.', 'error');
                    }
                })
                .catch(error => {
                    showAlert('apiAlert', '❌ خطأ في الاتصال: ' + error.message, 'error');
                });
        }

        // Message functions
        function sendMessage(phone, message) {
            return new Promise((resolve, reject) => {
                if (!settings.instanceId || !settings.apiToken) {
                    reject('لم يتم تكوين إعدادات API');
                    return;
                }

                const url = `https://api.green-api.com/waInstance${settings.instanceId}/sendMessage/${settings.apiToken}`;
                const chatId = phone.endsWith('@c.us') ? phone : phone + '@c.us';

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        chatId: chatId,
                        message: message
                    })
                })
                .then(response => {
                    if (response.ok) {
                        resolve('تم الإرسال بنجاح');
                    } else {
                        reject('فشل الإرسال: ' + response.status);
                    }
                })
                .catch(error => {
                    reject('خطأ في الإرسال: ' + error.message);
                });
            });
        }

        function sendSingleMessage() {
            const phone = document.getElementById('singlePhone').value.trim();
            const message = document.getElementById('singleMessage').value.trim();

            if (!phone || !message) {
                showAlert('singleAlert', 'يرجى إدخال رقم الهاتف والرسالة', 'error');
                return;
            }

            sendMessage(phone, message)
                .then(result => {
                    showAlert('singleAlert', '✅ ' + result, 'success');
                    document.getElementById('singlePhone').value = '';
                    document.getElementById('singleMessage').value = '';
                })
                .catch(error => {
                    showAlert('singleAlert', '❌ ' + error, 'error');
                });
        }

        function sendBulkMessages() {
            const numbersText = document.getElementById('bulkNumbers').value.trim();
            const message = document.getElementById('bulkMessage').value.trim();
            const delay = parseInt(document.getElementById('delaySeconds').value) * 1000;

            if (!numbersText || !message) {
                showAlert('bulkAlert', 'يرجى إدخال الأرقام والرسالة', 'error');
                return;
            }

            const numbers = numbersText.split('\n')
                .map(line => line.trim())
                .filter(line => line && /^\d+$/.test(line));

            if (numbers.length === 0) {
                showAlert('bulkAlert', 'لم يتم العثور على أرقام صحيحة', 'error');
                return;
            }

            if (!confirm(`هل تريد إرسال الرسالة إلى ${numbers.length} رقم؟`)) {
                return;
            }

            // Show progress
            document.getElementById('bulkProgress').style.display = 'block';
            document.getElementById('bulkStatus').textContent = 'جاري الإرسال...';

            let successCount = 0;
            let failedCount = 0;

            async function sendToNumbers() {
                for (let i = 0; i < numbers.length; i++) {
                    try {
                        await sendMessage(numbers[i], message);
                        successCount++;
                        updateProgress(i + 1, numbers.length, `✅ تم إرسال ${i + 1}/${numbers.length}`);
                    } catch (error) {
                        failedCount++;
                        updateProgress(i + 1, numbers.length, `❌ فشل ${i + 1}/${numbers.length}`);
                    }

                    if (i < numbers.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }

                // Final results
                document.getElementById('bulkProgress').style.display = 'none';
                showAlert('bulkAlert', `تم الانتهاء! ناجح: ${successCount}, فاشل: ${failedCount}`, 'info');
            }

            sendToNumbers();
        }

        function updateProgress(current, total, status) {
            const percentage = (current / total) * 100;
            document.getElementById('bulkProgressBar').style.width = percentage + '%';
            document.getElementById('bulkStatus').textContent = status;
        }

        // Template functions
        function loadTemplate() {
            const templateName = document.getElementById('messageTemplates').value;
            if (templateName && messageTemplates[templateName]) {
                document.getElementById('singleMessage').value = messageTemplates[templateName];
            }
        }

        function previewMessage() {
            const message = document.getElementById('singleMessage').value;
            if (message) {
                alert('معاينة الرسالة:\n\n' + message);
            } else {
                alert('لا توجد رسالة للمعاينة');
            }
        }

        function clearMessage() {
            document.getElementById('singleMessage').value = '';
            document.getElementById('messageTemplates').value = '';
        }

        // Groups functions
        function saveGroup() {
            const groupName = document.getElementById('groupName').value.trim();
            const numbersText = document.getElementById('groupNumbers').value.trim();

            if (!groupName || !numbersText) {
                showAlert('groupsAlert', 'يرجى إدخال اسم المجموعة والأرقام', 'error');
                return;
            }

            const numbers = numbersText.split('\n')
                .map(line => line.trim())
                .filter(line => line && /^\d+$/.test(line));

            if (numbers.length === 0) {
                showAlert('groupsAlert', 'لم يتم العثور على أرقام صحيحة', 'error');
                return;
            }

            groups[groupName] = numbers;
            localStorage.setItem('whatsappGroups', JSON.stringify(groups));
            
            showAlert('groupsAlert', `تم حفظ المجموعة "${groupName}" بنجاح! (${numbers.length} رقم)`, 'success');
            
            document.getElementById('groupName').value = '';
            document.getElementById('groupNumbers').value = '';
            refreshGroups();
        }

        function loadGroups() {
            const saved = localStorage.getItem('whatsappGroups');
            if (saved) {
                groups = JSON.parse(saved);
                refreshGroups();
            }
        }

        function refreshGroups() {
            const groupsList = document.getElementById('groupsList');
            
            if (Object.keys(groups).length === 0) {
                groupsList.innerHTML = '<p style="text-align: center; color: #666;">لا توجد مجموعات محفوظة</p>';
                return;
            }

            let html = '';
            for (const [name, numbers] of Object.entries(groups)) {
                html += `
                    <div class="number-item">
                        <strong>${name}</strong> (${numbers.length} رقم)
                        <button class="btn btn-secondary" style="float: left; padding: 5px 10px; font-size: 0.8rem;" onclick="loadGroupToNumbers('${name}')">تحميل</button>
                        <button class="btn btn-danger" style="float: left; padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="deleteGroup('${name}')">حذف</button>
                    </div>
                `;
            }
            groupsList.innerHTML = html;
        }

        function loadGroupToNumbers(groupName) {
            if (groups[groupName]) {
                document.getElementById('bulkNumbers').value = groups[groupName].join('\n');
                showTab('bulk');
                showAlert('bulkAlert', `تم تحميل مجموعة "${groupName}" (${groups[groupName].length} رقم)`, 'success');
            }
        }

        function deleteGroup(groupName) {
            if (confirm(`هل تريد حذف المجموعة "${groupName}"؟`)) {
                delete groups[groupName];
                localStorage.setItem('whatsappGroups', JSON.stringify(groups));
                refreshGroups();
                showAlert('groupsAlert', `تم حذف المجموعة "${groupName}"`, 'info');
            }
        }

        function clearAllGroups() {
            if (confirm('هل تريد حذف جميع المجموعات؟')) {
                groups = {};
                localStorage.setItem('whatsappGroups', JSON.stringify(groups));
                refreshGroups();
                showAlert('groupsAlert', 'تم حذف جميع المجموعات', 'info');
            }
        }

        function loadFromGroup() {
            if (Object.keys(groups).length === 0) {
                showAlert('bulkAlert', 'لا توجد مجموعات محفوظة', 'error');
                return;
            }

            let groupOptions = '';
            for (const name of Object.keys(groups)) {
                groupOptions += `<option value="${name}">${name} (${groups[name].length} رقم)</option>`;
            }

            const selectedGroup = prompt(`اختر المجموعة:\n\n${Object.keys(groups).map((name, index) => `${index + 1}. ${name} (${groups[name].length} رقم)`).join('\n')}\n\nأدخل اسم المجموعة:`);
            
            if (selectedGroup && groups[selectedGroup]) {
                loadGroupToNumbers(selectedGroup);
            }
        }

        function loadNumbersFromFile() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.txt,.csv';
            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const content = e.target.result;
                        const numbers = content.split('\n')
                            .map(line => line.trim())
                            .filter(line => line && !line.startsWith('#'))
                            .map(line => line.replace(/\D/g, ''))
                            .filter(line => line.length >= 10);
                        
                        document.getElementById('bulkNumbers').value = numbers.join('\n');
                        showAlert('bulkAlert', `تم تحميل ${numbers.length} رقم من الملف`, 'success');
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // Utility functions
        function showAlert(elementId, message, type) {
            const alert = document.getElementById(elementId);
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
