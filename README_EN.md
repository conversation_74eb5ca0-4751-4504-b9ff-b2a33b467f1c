# 📱 WhatsApp Message Sender

A simple and effective tool for sending WhatsApp messages using Node.js and the `whatsapp-web.js` library.

## ✨ Features

- 🔐 **Safe & Secure**: Uses official WhatsApp Web API
- 📱 **Easy to Use**: Simple interactive interface
- 🚀 **Fast**: Instant message sending
- 📊 **Bulk Sending**: Send messages to multiple numbers
- ✅ **Number Validation**: Checks number validity before sending
- 🔄 **Session Saving**: No need to scan QR Code every time

## 📋 Requirements

- Node.js (version 14 or newer)
- npm (package manager)
- Internet connection
- Smartphone with WhatsApp

## 🚀 Installation and Running

### 1. Install Dependencies
```bash
npm install
```

### 2. Run the Program
Choose one of these methods:

#### Method 1: Simple Run
```bash
run.bat
```

#### Method 2: Interactive Menu
```bash
start.bat
```

#### Method 3: Direct Command
```bash
node whatsapp-sender.js
```

#### Method 4: Quick Send
```bash
node quick-send.js
```

### 3. Link Your Device
1. A QR Code will appear in the terminal
2. Open WhatsApp on your phone
3. Go to: **Settings** > **Linked Devices** > **Link a Device**
4. Scan the QR Code displayed
5. Wait for successful login

## 📖 How to Use

### Send Single Message
1. Choose option `1` from the menu
2. Enter phone number (example: `01234567890`)
3. Enter message text
4. Press Enter to send

### Send Message to Multiple Numbers
1. Choose option `2` from the menu
2. Enter numbers separated by comma (example: `01234567890,01987654321`)
3. Enter message text
4. Press Enter to send

## 📝 Phone Number Formats

The tool supports different number formats:
- `01234567890` (Egyptian number without country code)
- `201234567890` (Egyptian number with country code)
- `+201234567890` (Egyptian number with country code and plus sign)

## ⚠️ Important Warnings

1. **Do NOT use for spam messages**
2. **Respect others' privacy**
3. **Ensure recipients consent to messages**
4. **Do not share sensitive information**
5. **Use responsibly**

## 🔧 Troubleshooting

### Problem: QR Code doesn't appear
- Make sure all libraries are installed
- Restart the program

### Problem: Failed to send message
- Check phone number validity
- Ensure number is registered on WhatsApp
- Check internet connection

### Problem: Connection lost
- Restart the program
- Check internet stability

## 📁 Project Files

```
whatsapp-sender/
├── package.json              # Project info and dependencies
├── whatsapp-sender.js        # Main program file
├── quick-send.js            # Quick send with pre-configured settings
├── test-simple.js           # Library test file
├── start.bat               # Windows launcher with menu
├── run.bat                 # Simple Windows launcher
├── README_EN.md           # English documentation
└── .wwebjs_auth/          # Session data folder (auto-created)
```

## 🛡️ Security and Privacy

- Uses official WhatsApp Web API
- No messages are saved or logged
- Session data is stored locally only
- No data sent to external servers

## 📞 Support

If you encounter any issues:
- Check the troubleshooting section above
- Make sure Node.js is updated to latest version
- Reinstall dependencies with `npm install`

## 🎯 Quick Start Commands

```bash
# Test libraries
node test-simple.js

# Run main program
node whatsapp-sender.js

# Quick send (edit config first)
node quick-send.js

# Windows launcher
start.bat
```

## 📄 License

This project is open source and available for personal and educational use.

---

**Note**: Please use this tool responsibly and respect WhatsApp's terms of service.
