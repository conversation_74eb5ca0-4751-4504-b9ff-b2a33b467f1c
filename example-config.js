// ملف مثال للإعدادات السريعة
// انسخ هذا المحتوى إلى quick-send.js واستبدل QUICK_CONFIG

const EXAMPLE_CONFIG = {
    // مثال 1: إرسال رسالة واحدة
    phoneNumber: '01234567890',  // ضع الرقم هنا
    message: 'مرحباً! هذه رسالة تجريبية من أداة WhatsApp',

    // مثال 2: إرسال رسالة لعدة أرقام
    phoneNumbers: [
        '01234567890',
        '01987654321',
        '01555666777'
    ],
    bulkMessage: 'رسالة جماعية لجميع الأرقام المحددة'
};

// أمثلة أخرى للاستخدام:

// إرسال رسالة ترحيب
const WELCOME_CONFIG = {
    phoneNumbers: ['01234567890', '01987654321'],
    bulkMessage: `مرحباً بك! 
نحن سعداء لانضمامك إلينا.
شكراً لك على اختيارنا.`
};

// إرسال تذكير
const REMINDER_CONFIG = {
    phoneNumbers: ['01234567890'],
    bulkMessage: `تذكير مهم:
لديك موعد غداً في تمام الساعة 3 مساءً.
يرجى التأكيد على الحضور.`
};

// إرسال إعلان
const ANNOUNCEMENT_CONFIG = {
    phoneNumbers: [
        '01234567890',
        '01987654321',
        '01555666777',
        '01444333222'
    ],
    bulkMessage: `🎉 إعلان مهم!
لدينا عرض خاص جديد.
تواصل معنا للمزيد من التفاصيل.`
};

console.log('📋 أمثلة الإعدادات:');
console.log('1. EXAMPLE_CONFIG - مثال عام');
console.log('2. WELCOME_CONFIG - رسالة ترحيب');
console.log('3. REMINDER_CONFIG - تذكير');
console.log('4. ANNOUNCEMENT_CONFIG - إعلان');
console.log('\n📝 لاستخدام أي من هذه الإعدادات:');
console.log('1. انسخ الإعدادات المطلوبة');
console.log('2. الصقها في quick-send.js مكان QUICK_CONFIG');
console.log('3. عدل الأرقام والرسائل حسب احتياجك');
console.log('4. شغل البرنامج: npm run quick');
