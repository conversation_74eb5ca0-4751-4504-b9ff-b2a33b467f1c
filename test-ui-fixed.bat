@echo off
title Test UI Fixed - WhatsApp Sender Pro
color 0A

echo.
echo ==========================================
echo       Test UI Fixed - WhatsApp Sender Pro
echo ==========================================
echo.

echo [STEP 1] Checking project files...
if not exist "src\index.html" (
    echo ❌ CRITICAL: src\index.html missing!
    echo The main UI file is not found.
    echo Please make sure you have the complete project files.
    goto :end
)

if not exist "src\styles.css" (
    echo ❌ CRITICAL: src\styles.css missing!
    echo The CSS file is not found.
    goto :end
)

if not exist "src\app.js" (
    echo ❌ CRITICAL: src\app.js missing!
    echo The JavaScript file is not found.
    goto :end
)

echo ✅ All UI files found

echo.
echo [STEP 2] Creating test data...
if not exist "numbers-groups" mkdir "numbers-groups"

echo # Test customers > "numbers-groups\customers.txt"
echo 01012345678 >> "numbers-groups\customers.txt"
echo 01123456789 >> "numbers-groups\customers.txt"
echo 01234567890 >> "numbers-groups\customers.txt"

echo # Test prospects > "numbers-groups\prospects.txt"
echo 01555666777 >> "numbers-groups\prospects.txt"
echo 01888999000 >> "numbers-groups\prospects.txt"

echo ✅ Test data created

echo.
echo [STEP 3] Fixing file permissions...
attrib -r "src\*.*" /s >nul 2>&1
echo ✅ File permissions fixed

echo.
echo [STEP 4] Testing different browsers...
echo.

echo Trying to open with different methods...
echo.

REM Method 1: Default browser
echo Method 1: Default browser...
start "" "src\index.html"
timeout /t 2 >nul

REM Method 2: Chrome
echo Method 2: Chrome (if available)...
start chrome "src\index.html" >nul 2>&1
timeout /t 2 >nul

REM Method 3: Firefox
echo Method 3: Firefox (if available)...
start firefox "src\index.html" >nul 2>&1
timeout /t 2 >nul

REM Method 4: Edge
echo Method 4: Edge...
start msedge "src\index.html" >nul 2>&1
timeout /t 2 >nul

echo.
echo ✅ UI should now be open in one or more browsers

echo.
echo ==========================================
echo           UI TEST INSTRUCTIONS
echo ==========================================
echo.

echo The WhatsApp Sender Pro interface should now be open.
echo.

echo WHAT TO TEST:
echo.
echo ✅ NAVIGATION:
echo    - Click on Dashboard, Single Message, Bulk Messages tabs
echo    - Check if all tabs switch properly
echo.
echo ✅ DESIGN:
echo    - Beautiful gradient background
echo    - Modern glass-style cards
echo    - Professional colors and fonts
echo.
echo ✅ FORMS:
echo    - Type in phone number fields
echo    - Type in message text areas
echo    - Click buttons and see responses
echo.
echo ✅ FEATURES:
echo    - Connection status indicator
echo    - Quick action buttons
echo    - Settings panel
echo    - Number groups section
echo.

echo EXPECTED BEHAVIOR:
echo ✅ Smooth animations and transitions
echo ✅ Toast notifications when clicking buttons
echo ✅ Form validation messages
echo ✅ Responsive design that fits your screen
echo.

echo NOTE: In browser mode, actual messaging won't work.
echo This is normal - we're only testing the interface.
echo.

echo.
echo Did the UI open and look good? (y/n)
set /p ui_result="Enter your answer: "

if /i "%ui_result%"=="y" (
    echo.
    echo ✅ EXCELLENT! Your UI is working perfectly.
    echo.
    echo The interface is ready. Now you can:
    echo.
    echo 1. build-simple.bat     ← Build desktop version
    echo 2. npm start            ← Test with Electron
    echo 3. fix-all-problems.bat ← If you get EBUSY errors
    echo.
    
    echo Would you like to try building the desktop version? (y/n)
    set /p build_choice="Enter your choice: "
    
    if /i "%build_choice%"=="y" (
        echo.
        echo Starting simple build process...
        timeout /t 2 >nul
        call build-simple.bat
    )
    
) else (
    echo.
    echo ❌ UI test failed. Let's troubleshoot:
    echo.
    echo COMMON ISSUES AND SOLUTIONS:
    echo.
    echo 1. BROWSER SECURITY:
    echo    - Try a different browser (Chrome, Firefox, Edge)
    echo    - Allow local file access in browser settings
    echo.
    echo 2. FILE CORRUPTION:
    echo    - Check if src\index.html opens in notepad
    echo    - Verify file sizes are not 0 bytes
    echo.
    echo 3. ANTIVIRUS BLOCKING:
    echo    - Temporarily disable antivirus
    echo    - Add project folder to antivirus exceptions
    echo.
    echo 4. WINDOWS PERMISSIONS:
    echo    - Run this script as Administrator
    echo    - Check folder permissions
    echo.
    
    echo.
    echo MANUAL TEST:
    echo 1. Open Windows Explorer
    echo 2. Navigate to the 'src' folder
    echo 3. Double-click on 'index.html'
    echo 4. It should open in your browser
    echo.
    
    echo If manual test works, the issue is with this script.
    echo If manual test fails, the issue is with your system/browser.
)

:end
echo.
echo ==========================================
echo Press ANY KEY to close this window
echo ==========================================
pause
exit
