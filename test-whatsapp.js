const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');

console.log('🚀 بدء اختبار WhatsApp...');
console.log('⏳ جاري تحميل المتصفح... (قد يستغرق دقيقة)');

const client = new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
        ]
    }
});

// عرض QR Code
client.on('qr', (qr) => {
    console.log('\n🔗 امسح الـ QR Code التالي بهاتفك:');
    qrcode.generate(qr, { small: true });
    console.log('\n📱 افتح WhatsApp > الإعدادات > الأجهزة المرتبطة > ربط جهاز');
});

// عند تسجيل الدخول بنجاح
client.on('ready', () => {
    console.log('\n✅ تم تسجيل الدخول بنجاح!');
    console.log('🎉 الأداة جاهزة للاستخدام!');
    
    // إغلاق البرنامج بعد 5 ثوان
    setTimeout(() => {
        console.log('\n👋 إغلاق الاختبار...');
        client.destroy();
        process.exit(0);
    }, 5000);
});

// معالجة الأخطاء
client.on('auth_failure', (msg) => {
    console.error('\n❌ فشل في المصادقة:', msg);
});

client.on('disconnected', (reason) => {
    console.log('\n❌ تم قطع الاتصال:', reason);
});

// بدء التشغيل
console.log('🔄 جاري التهيئة...');
client.initialize().catch(error => {
    console.error('❌ خطأ في التهيئة:', error.message);
    process.exit(1);
});

// إغلاق البرنامج عند الضغط على Ctrl+C
process.on('SIGINT', () => {
    console.log('\n\n👋 جاري إغلاق البرنامج...');
    client.destroy();
    process.exit(0);
});
