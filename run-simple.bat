@echo off
chcp 65001 >nul
title WhatsApp Sender - اختبار سريع

echo ========================================
echo    اختبار أداة إرسال رسائل WhatsApp
echo ========================================
echo.

echo 1. فحص Node.js...
node --version
if errorlevel 1 (
    echo ❌ Node.js غير مثبت! يرجى تثبيت Node.js أولاً
    pause
    exit
)
echo ✅ Node.js مثبت بنجاح

echo.
echo 2. فحص المكتبات...
node test-simple.js
if errorlevel 1 (
    echo ❌ خطأ في المكتبات! يرجى تشغيل: npm install
    pause
    exit
)

echo.
echo 3. هل تريد تشغيل البرنامج الرئيسي؟ (y/n)
set /p run_main="أدخل y للتشغيل أو n للخروج: "

if /i "%run_main%"=="y" (
    echo.
    echo جاري تشغيل البرنامج...
    echo ملاحظة: قد يستغرق دقيقة لتحميل المتصفح
    echo.
    node whatsapp-sender.js
) else (
    echo.
    echo شكراً! يمكنك تشغيل البرنامج لاحقاً بالأمر: node whatsapp-sender.js
)

echo.
pause
