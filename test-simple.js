console.log('🚀 بدء اختبار المكتبات...');

try {
    console.log('📦 تحميل مكتبة whatsapp-web.js...');
    const { Client } = require('whatsapp-web.js');
    console.log('✅ تم تحميل whatsapp-web.js بنجاح');

    console.log('📦 تحميل مكتبة qrcode-terminal...');
    const qrcode = require('qrcode-terminal');
    console.log('✅ تم تحميل qrcode-terminal بنجاح');

    console.log('📦 تحميل مكتبة readline...');
    const readline = require('readline');
    console.log('✅ تم تحميل readline بنجاح');

    console.log('\n🎉 جميع المكتبات تعمل بشكل صحيح!');
    console.log('💡 يمكنك الآن تشغيل البرنامج الرئيسي');

} catch (error) {
    console.error('❌ خطأ في تحميل المكتبات:', error.message);
    console.log('\n🔧 حلول مقترحة:');
    console.log('1. تشغيل: npm install');
    console.log('2. تحديث Node.js إلى أحدث إصدار');
    console.log('3. حذف node_modules وإعادة التثبيت');
}
