console.log('🚀 Starting library test...');

try {
    console.log('📦 Loading whatsapp-web.js library...');
    const { Client } = require('whatsapp-web.js');
    console.log('✅ whatsapp-web.js loaded successfully');

    console.log('📦 Loading qrcode-terminal library...');
    const qrcode = require('qrcode-terminal');
    console.log('✅ qrcode-terminal loaded successfully');

    console.log('📦 Loading readline library...');
    const readline = require('readline');
    console.log('✅ readline loaded successfully');

    console.log('\n🎉 All libraries are working correctly!');
    console.log('💡 You can now run the main program');

} catch (error) {
    console.error('❌ Error loading libraries:', error.message);
    console.log('\n🔧 Suggested solutions:');
    console.log('1. Run: npm install');
    console.log('2. Update Node.js to latest version');
    console.log('3. Delete node_modules and reinstall');
}
