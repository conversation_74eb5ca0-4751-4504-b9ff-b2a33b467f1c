@echo off
title Emergency Test - WhatsApp Sender Pro
color 0F

echo.
echo ==========================================
echo      Emergency Test - Last Resort
echo ==========================================
echo.

echo If all other methods failed, this is the manual approach.
echo.

echo [TEST 1] Basic file check...
if exist "src\index.html" (
    echo ✅ index.html exists
) else (
    echo ❌ index.html MISSING - Critical error!
    goto :critical_error
)

if exist "src\styles.css" (
    echo ✅ styles.css exists
) else (
    echo ❌ styles.css MISSING - Critical error!
    goto :critical_error
)

if exist "src\app.js" (
    echo ✅ app.js exists
) else (
    echo ❌ app.js MISSING - Critical error!
    goto :critical_error
)

echo.
echo [TEST 2] Manual UI test...
echo Opening index.html directly...
echo.

REM Try multiple methods to open the file
echo Method 1: Windows default...
start "" "%CD%\src\index.html"

echo Method 2: Full path...
start "" "file:///%CD:\=/%/src/index.html"

echo Method 3: Explorer...
explorer "%CD%\src"

echo.
echo [TEST 3] Node.js availability...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not available
    echo.
    echo CRITICAL: You must install Node.js first!
    echo 1. Go to: https://nodejs.org
    echo 2. Download LTS version
    echo 3. Install and restart computer
    echo.
    goto :end
) else (
    echo ✅ Node.js available
)

echo.
echo [TEST 4] Creating minimal package.json...
if not exist "package.json" (
    echo Creating minimal package.json...
    echo { > package.json
    echo   "name": "whatsapp-sender-pro", >> package.json
    echo   "version": "1.0.0", >> package.json
    echo   "main": "electron/main.js", >> package.json
    echo   "scripts": { >> package.json
    echo     "start": "electron ." >> package.json
    echo   } >> package.json
    echo } >> package.json
    echo ✅ Minimal package.json created
)

echo.
echo [TEST 5] Emergency Electron install...
echo Installing only Electron (minimal)...
npm install electron --save-dev --no-optional --force --silent
if errorlevel 1 (
    echo ❌ Even minimal install failed!
    echo.
    echo SOLUTIONS:
    echo 1. Restart computer
    echo 2. Run as Administrator  
    echo 3. Disable antivirus
    echo 4. Check internet connection
    echo 5. Try on different computer
    goto :end
) else (
    echo ✅ Minimal Electron installed
)

echo.
echo [TEST 6] Emergency app test...
echo Testing if app can start...
timeout /t 2 >nul
start /min npm start
timeout /t 5 >nul
taskkill /f /im electron.exe >nul 2>&1
echo ✅ Emergency test completed

echo.
echo ==========================================
echo         EMERGENCY TEST RESULTS
echo ==========================================
echo.

echo If you saw the WhatsApp Sender Pro window open:
echo ✅ SUCCESS - Your app is working!
echo.
echo Next steps:
echo 1. The UI test should have opened in browser
echo 2. The Electron app should have opened briefly
echo 3. You can now try: build-simple.bat
echo.

echo If nothing opened:
echo ❌ CRITICAL SYSTEM ISSUE
echo.
echo Possible causes:
echo 1. Antivirus blocking everything
echo 2. Windows permissions issue
echo 3. Corrupted Node.js installation
echo 4. Missing system dependencies
echo.

goto :end

:critical_error
echo.
echo ==========================================
echo           CRITICAL ERROR
echo ==========================================
echo.
echo Essential project files are missing!
echo.
echo This means:
echo 1. Incomplete download/extraction
echo 2. Files were deleted by antivirus
echo 3. Wrong directory
echo.
echo SOLUTION:
echo 1. Re-download the complete project
echo 2. Extract to a new folder
echo 3. Add folder to antivirus exceptions
echo 4. Run this test again
echo.

:end
echo.
echo ==========================================
echo Press ANY KEY to close this window
echo ==========================================
pause
exit
