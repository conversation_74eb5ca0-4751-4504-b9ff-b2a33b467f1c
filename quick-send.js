const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');

// إعدادات سريعة - يمكنك تعديلها هنا
const QUICK_CONFIG = {
    // ضع الرقم والرسالة هنا للإرسال السريع
    phoneNumber: '', // مثال: '01234567890'
    message: '',     // مثال: 'مرحباً! هذه رسالة تجريبية'
    
    // أو قائمة أرقام متعددة
    phoneNumbers: [], // مثال: ['01234567890', '01987654321']
    
    // رسالة للإرسال المتعدد
    bulkMessage: ''   // مثال: 'رسالة جماعية'
};

class QuickWhatsAppSender {
    constructor() {
        this.client = new Client({
            authStrategy: new LocalAuth(),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            }
        });

        this.setupEventHandlers();
    }

    setupEventHandlers() {
        this.client.on('qr', (qr) => {
            console.log('\n🔗 امسح الـ QR Code التالي بهاتفك:');
            qrcode.generate(qr, { small: true });
        });

        this.client.on('ready', async () => {
            console.log('\n✅ تم تسجيل الدخول بنجاح!');
            await this.executeQuickSend();
        });

        this.client.on('disconnected', (reason) => {
            console.log('\n❌ تم قطع الاتصال:', reason);
        });
    }

    formatPhoneNumber(phoneNumber) {
        let cleanNumber = phoneNumber.replace(/\D/g, '');
        
        if (!cleanNumber.startsWith('20') && cleanNumber.length === 11) {
            cleanNumber = '20' + cleanNumber;
        } else if (!cleanNumber.startsWith('20') && cleanNumber.length === 10) {
            cleanNumber = '20' + cleanNumber;
        }
        
        return cleanNumber + '@c.us';
    }

    async sendMessage(phoneNumber, message) {
        try {
            const formattedNumber = this.formatPhoneNumber(phoneNumber);
            await this.client.sendMessage(formattedNumber, message);
            console.log(`✅ تم إرسال الرسالة إلى: ${phoneNumber}`);
            return true;
        } catch (error) {
            console.error(`❌ فشل إرسال الرسالة إلى ${phoneNumber}:`, error.message);
            return false;
        }
    }

    async executeQuickSend() {
        console.log('\n🚀 بدء الإرسال السريع...');

        // إرسال رسالة واحدة
        if (QUICK_CONFIG.phoneNumber && QUICK_CONFIG.message) {
            console.log('\n📤 إرسال رسالة واحدة...');
            await this.sendMessage(QUICK_CONFIG.phoneNumber, QUICK_CONFIG.message);
        }

        // إرسال رسائل متعددة
        if (QUICK_CONFIG.phoneNumbers.length > 0 && QUICK_CONFIG.bulkMessage) {
            console.log('\n📤 إرسال رسائل متعددة...');
            
            for (let i = 0; i < QUICK_CONFIG.phoneNumbers.length; i++) {
                const phoneNumber = QUICK_CONFIG.phoneNumbers[i];
                console.log(`\n📱 إرسال للرقم ${i + 1}/${QUICK_CONFIG.phoneNumbers.length}: ${phoneNumber}`);
                
                await this.sendMessage(phoneNumber, QUICK_CONFIG.bulkMessage);
                
                // انتظار ثانيتين بين كل رسالة
                if (i < QUICK_CONFIG.phoneNumbers.length - 1) {
                    console.log('⏳ انتظار ثانيتين...');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
        }

        // إذا لم يتم تعيين أي إعدادات
        if (!QUICK_CONFIG.phoneNumber && !QUICK_CONFIG.message && 
            QUICK_CONFIG.phoneNumbers.length === 0 && !QUICK_CONFIG.bulkMessage) {
            console.log('\n⚠️  لم يتم تعيين أي إعدادات للإرسال السريع');
            console.log('📝 يرجى تعديل ملف quick-send.js وإضافة:');
            console.log('   - رقم الهاتف والرسالة للإرسال الواحد');
            console.log('   - أو قائمة أرقام ورسالة للإرسال المتعدد');
            console.log('\nمثال:');
            console.log('QUICK_CONFIG.phoneNumber = "01234567890";');
            console.log('QUICK_CONFIG.message = "مرحباً!";');
        }

        console.log('\n✅ تم الانتهاء من جميع العمليات!');
        console.log('👋 إغلاق البرنامج...');
        
        setTimeout(() => {
            this.client.destroy();
            process.exit(0);
        }, 3000);
    }

    async start() {
        console.log('🚀 أداة الإرسال السريع لـ WhatsApp');
        console.log('⏳ جاري التحضير...\n');
        
        try {
            await this.client.initialize();
        } catch (error) {
            console.error('❌ خطأ في بدء التشغيل:', error);
            process.exit(1);
        }
    }
}

// عرض التعليمات
console.log('📋 تعليمات الاستخدام:');
console.log('1. تأكد من تعديل إعدادات QUICK_CONFIG في أعلى الملف');
console.log('2. أضف رقم الهاتف والرسالة للإرسال السريع');
console.log('3. شغل البرنامج وامسح QR Code');
console.log('4. سيتم الإرسال تلقائياً\n');

// بدء التشغيل
const quickSender = new QuickWhatsAppSender();
quickSender.start();

// معالجة إغلاق البرنامج
process.on('SIGINT', () => {
    console.log('\n\n👋 جاري إغلاق البرنامج...');
    process.exit(0);
});
