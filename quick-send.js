const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');

// Quick settings - you can modify these here
const QUICK_CONFIG = {
    // Put phone number and message here for quick sending
    phoneNumber: '', // example: '01234567890'
    message: '',     // example: 'Hello! This is a test message'

    // Or list of multiple numbers
    phoneNumbers: [], // example: ['01234567890', '01987654321']

    // Message for bulk sending
    bulkMessage: ''   // example: 'Bulk message for everyone'
};

class QuickWhatsAppSender {
    constructor() {
        this.client = new Client({
            authStrategy: new LocalAuth(),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            }
        });

        this.setupEventHandlers();
    }

    setupEventHandlers() {
        this.client.on('qr', (qr) => {
            console.log('\n🔗 Scan the following QR Code with your phone:');
            qrcode.generate(qr, { small: true });
        });

        this.client.on('ready', async () => {
            console.log('\n✅ Login successful!');
            await this.executeQuickSend();
        });

        this.client.on('disconnected', (reason) => {
            console.log('\n❌ Disconnected:', reason);
        });
    }

    formatPhoneNumber(phoneNumber) {
        let cleanNumber = phoneNumber.replace(/\D/g, '');

        if (!cleanNumber.startsWith('20') && cleanNumber.length === 11) {
            cleanNumber = '20' + cleanNumber;
        } else if (!cleanNumber.startsWith('20') && cleanNumber.length === 10) {
            cleanNumber = '20' + cleanNumber;
        }

        return cleanNumber + '@c.us';
    }

    async sendMessage(phoneNumber, message) {
        try {
            const formattedNumber = this.formatPhoneNumber(phoneNumber);
            await this.client.sendMessage(formattedNumber, message);
            console.log(`✅ Message sent to: ${phoneNumber}`);
            return true;
        } catch (error) {
            console.error(`❌ Failed to send message to ${phoneNumber}:`, error.message);
            return false;
        }
    }

    async executeQuickSend() {
        console.log('\n🚀 Starting quick send...');

        // Send single message
        if (QUICK_CONFIG.phoneNumber && QUICK_CONFIG.message) {
            console.log('\n📤 Sending single message...');
            await this.sendMessage(QUICK_CONFIG.phoneNumber, QUICK_CONFIG.message);
        }

        // Send multiple messages
        if (QUICK_CONFIG.phoneNumbers.length > 0 && QUICK_CONFIG.bulkMessage) {
            console.log('\n📤 Sending multiple messages...');

            for (let i = 0; i < QUICK_CONFIG.phoneNumbers.length; i++) {
                const phoneNumber = QUICK_CONFIG.phoneNumbers[i];
                console.log(`\n📱 Sending to number ${i + 1}/${QUICK_CONFIG.phoneNumbers.length}: ${phoneNumber}`);

                await this.sendMessage(phoneNumber, QUICK_CONFIG.bulkMessage);

                // Wait 2 seconds between messages
                if (i < QUICK_CONFIG.phoneNumbers.length - 1) {
                    console.log('⏳ Waiting 2 seconds...');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
        }

        // If no settings are configured
        if (!QUICK_CONFIG.phoneNumber && !QUICK_CONFIG.message &&
            QUICK_CONFIG.phoneNumbers.length === 0 && !QUICK_CONFIG.bulkMessage) {
            console.log('\n⚠️  No quick send settings configured');
            console.log('📝 Please edit quick-send.js file and add:');
            console.log('   - Phone number and message for single send');
            console.log('   - Or list of numbers and message for bulk send');
            console.log('\nExample:');
            console.log('QUICK_CONFIG.phoneNumber = "01234567890";');
            console.log('QUICK_CONFIG.message = "Hello!";');
        }

        console.log('\n✅ All operations completed!');
        console.log('👋 Closing program...');

        setTimeout(() => {
            this.client.destroy();
            process.exit(0);
        }, 3000);
    }

    async start() {
        console.log('🚀 WhatsApp Quick Sender Tool');
        console.log('⏳ Preparing...\n');

        try {
            await this.client.initialize();
        } catch (error) {
            console.error('❌ Startup error:', error);
            process.exit(1);
        }
    }
}

// Display instructions
console.log('📋 Usage Instructions:');
console.log('1. Make sure to edit QUICK_CONFIG settings at the top of the file');
console.log('2. Add phone number and message for quick sending');
console.log('3. Run the program and scan QR Code');
console.log('4. Messages will be sent automatically\n');

// Start the program
const quickSender = new QuickWhatsAppSender();
quickSender.start();

// Handle program termination
process.on('SIGINT', () => {
    console.log('\n\n👋 Closing program...');
    process.exit(0);
});
