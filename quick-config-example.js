// Quick Configuration Example for WhatsApp API Sender
// Copy this configuration to simple-api-sender.js

// =================================================================
// STEP 1: Get credentials from Green API
// =================================================================
// 1. Go to https://green-api.com
// 2. Register (free account)
// 3. Create instance
// 4. Get instanceId and apiToken
// 5. Scan QR code ONCE with your WhatsApp

// =================================================================
// STEP 2: Replace the CONFIG section in simple-api-sender.js
// =================================================================

const CONFIG = {
    // Replace these with your actual credentials from Green API
    instanceId: '**********',                    // Your instance ID from Green API
    apiToken: 'abc123def456ghi789jkl012mno345',  // Your API token from Green API
    
    // Replace with your WhatsApp number (the one you want to send FROM)
    senderNumber: '+************',               // Your WhatsApp number with country code
    
    // API URL (don't change this)
    apiUrl: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
};

// =================================================================
// EXAMPLE CONFIGURATIONS
// =================================================================

// Example 1: Egyptian number
const EXAMPLE_EGYPT = {
    instanceId: '**********',
    apiToken: 'abc123def456ghi789jkl012mno345pqr678stu901vwx234yz',
    senderNumber: '+************',  // Egyptian number
    apiUrl: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
};

// Example 2: Saudi number
const EXAMPLE_SAUDI = {
    instanceId: '1101654321',
    apiToken: 'xyz987wvu654tsr321qpo098nml765kji432hgf210edc098ba',
    senderNumber: '+966501234567',  // Saudi number
    apiUrl: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
};

// Example 3: UAE number
const EXAMPLE_UAE = {
    instanceId: '1101789012',
    apiToken: 'def456ghi789jkl012mno345pqr678stu901vwx234yza567bcd',
    senderNumber: '+************',  // UAE number
    apiUrl: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
};

// =================================================================
// HOW TO USE
// =================================================================

console.log('📋 Configuration Guide:');
console.log('');
console.log('1. Get Green API credentials:');
console.log('   • Visit: https://green-api.com');
console.log('   • Register and create instance');
console.log('   • Get instanceId and apiToken');
console.log('');
console.log('2. Link your WhatsApp:');
console.log('   • Scan QR code ONCE in Green API dashboard');
console.log('   • Your number will be linked');
console.log('');
console.log('3. Configure simple-api-sender.js:');
console.log('   • Replace instanceId with your instance ID');
console.log('   • Replace apiToken with your API token');
console.log('   • Replace senderNumber with your WhatsApp number');
console.log('');
console.log('4. Run the program:');
console.log('   • node simple-api-sender.js');
console.log('   • OR run-api.bat');
console.log('');
console.log('💡 Example configuration:');
console.log('   instanceId: "**********"');
console.log('   apiToken: "abc123def456..."');
console.log('   senderNumber: "+************"');
console.log('');
console.log('🎯 After setup, you can send messages from your number');
console.log('   without scanning QR code every time!');

// =================================================================
// TESTING CONFIGURATION
// =================================================================

function testConfig(config) {
    console.log('\n🔧 Testing configuration...');
    
    if (config.instanceId === 'YOUR_INSTANCE_ID' || 
        config.instanceId === '**********' ||
        config.apiToken === 'YOUR_API_TOKEN' ||
        config.apiToken === 'abc123def456ghi789jkl012mno345') {
        console.log('❌ Configuration not set up properly');
        console.log('📝 Please replace example values with your actual credentials');
        return false;
    }
    
    if (!config.senderNumber.startsWith('+')) {
        console.log('⚠️  Sender number should include country code (e.g., +************)');
    }
    
    console.log('✅ Configuration looks good!');
    return true;
}

// Test the example configuration
// testConfig(CONFIG);
