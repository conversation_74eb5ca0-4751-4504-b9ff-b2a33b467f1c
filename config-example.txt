// مثال للتكوين - انسخ هذا إلى marketing-identity-sender.js

const MARKETING_CONFIG = {
    // ضع رقم WhatsApp الخاص بك هنا
    realNumber: '+201012345678',  // مثال: رقمك الحقيقي
    
    // معلومات هويتك التسويقية
    identity: {
        businessName: 'شركة التسويق الذكي',           // اسم شركتك أو نشاطك
        contactPerson: 'أحمد محمد',                  // اسمك الحقيقي
        category: 'خدمات التسويق الرقمي',            // نوع الخدمة
        description: 'نساعد الشركات على زيادة مبيعاتها', // وصف الخدمة
        website: 'www.smart-marketing.com',          // موقعك (اختياري)
        email: '<EMAIL>'           // إيميلك
    },
    
    // بيانات Green API - احصل عليها من https://green-api.com
    api: {
        instanceId: '1101123456',                    // ضع Instance ID الخاص بك
        apiToken: 'abc123def456ghi789jkl012mno345',  // ضع API Token الخاص بك
        url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
    }
};

// خطوات الحصول على بيانات Green API:
// 1. اذهب إلى https://green-api.com
// 2. سجل حساب مجاني
// 3. أنشئ instance جديد
// 4. ستحصل على Instance ID و API Token
// 5. امسح QR Code مرة واحدة لربط WhatsApp
// 6. استبدل القيم أعلاه ببياناتك الحقيقية

// مثال آخر:
const EXAMPLE_CONFIG = {
    realNumber: '+966501234567',  // رقم سعودي
    
    identity: {
        businessName: 'مؤسسة الحلول التقنية',
        contactPerson: 'خالد العتيبي',
        category: 'تطوير المواقع والتطبيقات',
        description: 'نقدم حلول تقنية متطورة',
        website: 'www.tech-solutions.sa',
        email: '<EMAIL>'
    },
    
    api: {
        instanceId: '1101987654',
        apiToken: 'xyz789uvw456rst123opq890nml567kji234hgf901edc678',
        url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
    }
};
