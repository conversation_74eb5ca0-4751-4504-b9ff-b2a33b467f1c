@echo off
chcp 65001 >nul
title أداة إرسال رسائل WhatsApp

:menu
cls
echo.
echo ========================================
echo    أداة إرسال رسائل WhatsApp
echo ========================================
echo.
echo اختر طريقة التشغيل:
echo.
echo 1. التشغيل التفاعلي (مع قائمة)
echo 2. التشغيل السريع (إعدادات مسبقة)
echo 3. اختبار المكتبات
echo 4. الخروج
echo.
set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" (
    echo.
    echo جاري تشغيل الوضع التفاعلي...
    echo يرجى الانتظار...
    node whatsapp-sender.js
    goto :back_to_menu
) else if "%choice%"=="2" (
    echo.
    echo جاري تشغيل الوضع السريع...
    echo يرجى الانتظار...
    node quick-send.js
    goto :back_to_menu
) else if "%choice%"=="3" (
    echo.
    echo جاري اختبار المكتبات...
    node test-simple.js
    goto :back_to_menu
) else if "%choice%"=="4" (
    echo.
    echo شكراً لاستخدام الأداة!
    timeout /t 2 >nul
    exit
) else (
    echo.
    echo اختيار غير صحيح! يرجى اختيار رقم من 1 إلى 4
    timeout /t 3 >nul
    goto :menu
)

:back_to_menu
echo.
echo اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto :menu
