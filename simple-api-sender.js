const axios = require('axios');
const readline = require('readline');

// Simple configuration - Edit these values
const CONFIG = {
    // Green API Configuration (Recommended)
    instanceId: 'YOUR_INSTANCE_ID',     // Replace with your instance ID
    apiToken: 'YOUR_API_TOKEN',         // Replace with your API token
    
    // Your WhatsApp number (the one you want to send FROM)
    senderNumber: '+201234567890',      // Replace with your number
    
    // API URL (don't change unless you know what you're doing)
    apiUrl: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
};

class SimpleWhatsAppSender {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    // Format phone number
    formatPhoneNumber(phoneNumber) {
        let cleanNumber = phoneNumber.replace(/\D/g, '');
        
        // Add country code if not present (assuming Egypt +20)
        if (!cleanNumber.startsWith('20') && cleanNumber.length === 11) {
            cleanNumber = '20' + cleanNumber;
        } else if (!cleanNumber.startsWith('20') && cleanNumber.length === 10) {
            cleanNumber = '20' + cleanNumber;
        }
        
        return cleanNumber + '@c.us';
    }

    // Send message via Green API
    async sendMessage(toNumber, message) {
        // Check if API is configured
        if (CONFIG.instanceId === 'YOUR_INSTANCE_ID' || CONFIG.apiToken === 'YOUR_API_TOKEN') {
            console.log('❌ API not configured!');
            console.log('📝 Please edit the CONFIG section at the top of this file');
            console.log('💡 Get credentials from https://green-api.com');
            return false;
        }

        const formattedNumber = this.formatPhoneNumber(toNumber);
        const url = CONFIG.apiUrl
            .replace('{instanceId}', CONFIG.instanceId)
            .replace('{apiToken}', CONFIG.apiToken);
        
        const data = {
            chatId: formattedNumber,
            message: message
        };

        console.log(`\n📤 Sending from: ${CONFIG.senderNumber}`);
        console.log(`📱 Sending to: ${toNumber}`);
        console.log(`💬 Message: ${message}`);
        console.log(`⏳ Processing...`);

        try {
            const response = await axios.post(url, data, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.data && response.data.idMessage) {
                console.log('✅ Message sent successfully!');
                console.log(`📋 Message ID: ${response.data.idMessage}`);
                return true;
            } else {
                console.log('❌ Unexpected response:', response.data);
                return false;
            }
        } catch (error) {
            console.log('❌ Failed to send message');
            if (error.response) {
                console.log('🔍 Error:', error.response.data);
                console.log('📊 Status:', error.response.status);
            } else {
                console.log('🔍 Error:', error.message);
            }
            return false;
        }
    }

    // Show main menu
    showMenu() {
        console.log('\n' + '='.repeat(50));
        console.log('📱 Simple WhatsApp API Sender');
        console.log('📞 Sender: ' + CONFIG.senderNumber);
        console.log('='.repeat(50));
        console.log('1. Send single message');
        console.log('2. Send bulk messages');
        console.log('3. Test API connection');
        console.log('4. Configuration help');
        console.log('5. Exit');
        console.log('='.repeat(50));

        this.rl.question('Choose an option: ', (choice) => {
            this.handleMenuChoice(choice);
        });
    }

    // Handle menu choices
    handleMenuChoice(choice) {
        switch (choice) {
            case '1':
                this.sendSingleMessage();
                break;
            case '2':
                this.sendBulkMessages();
                break;
            case '3':
                this.testConnection();
                break;
            case '4':
                this.showConfigHelp();
                break;
            case '5':
                this.exit();
                break;
            default:
                console.log('❌ Invalid choice, please try again');
                this.showMenu();
        }
    }

    // Send single message
    sendSingleMessage() {
        console.log('\n📝 Send Single Message');
        console.log('-'.repeat(30));

        this.rl.question('Enter recipient number (example: 01234567890): ', (toNumber) => {
            if (!toNumber.trim()) {
                console.log('❌ Recipient number is required');
                this.showMenu();
                return;
            }

            this.rl.question('Enter message: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Message text is required');
                    this.showMenu();
                    return;
                }

                await this.sendMessage(toNumber, message);
                
                setTimeout(() => {
                    this.showMenu();
                }, 3000);
            });
        });
    }

    // Send bulk messages
    sendBulkMessages() {
        console.log('\n📝 Send Bulk Messages');
        console.log('-'.repeat(30));

        this.rl.question('Enter numbers separated by comma: ', (numbers) => {
            if (!numbers.trim()) {
                console.log('❌ Numbers are required');
                this.showMenu();
                return;
            }

            const numberList = numbers.split(',').map(num => num.trim()).filter(num => num);
            
            this.rl.question('Enter message: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Message text is required');
                    this.showMenu();
                    return;
                }

                console.log('\n⏳ Sending bulk messages...');
                let successCount = 0;
                
                for (let i = 0; i < numberList.length; i++) {
                    console.log(`\n📤 Sending ${i + 1}/${numberList.length} to: ${numberList[i]}`);
                    const success = await this.sendMessage(numberList[i], message);
                    
                    if (success) successCount++;
                    
                    // Wait between messages to avoid rate limiting
                    if (i < numberList.length - 1) {
                        console.log('⏳ Waiting 3 seconds...');
                        await new Promise(resolve => setTimeout(resolve, 3000));
                    }
                }

                console.log(`\n📊 Results: ${successCount}/${numberList.length} messages sent successfully`);
                
                setTimeout(() => {
                    this.showMenu();
                }, 3000);
            });
        });
    }

    // Test API connection
    async testConnection() {
        console.log('\n🔧 Testing API Connection...');
        
        if (CONFIG.instanceId === 'YOUR_INSTANCE_ID' || CONFIG.apiToken === 'YOUR_API_TOKEN') {
            console.log('❌ API not configured!');
            console.log('📝 Please configure the API first');
            this.showConfigHelp();
            return;
        }

        // Test with a simple API call
        const url = `https://api.green-api.com/waInstance${CONFIG.instanceId}/getSettings/${CONFIG.apiToken}`;
        
        try {
            console.log('⏳ Testing connection...');
            const response = await axios.get(url, { timeout: 10000 });
            
            if (response.data) {
                console.log('✅ API connection successful!');
                console.log('📱 WhatsApp status:', response.data.webhookUrl ? 'Connected' : 'Check connection');
                console.log('📋 Instance ID:', CONFIG.instanceId);
            }
        } catch (error) {
            console.log('❌ API connection failed');
            console.log('🔍 Error:', error.message);
            console.log('💡 Check your credentials and internet connection');
        }
        
        setTimeout(() => {
            this.showMenu();
        }, 3000);
    }

    // Show configuration help
    showConfigHelp() {
        console.log('\n📚 Configuration Help');
        console.log('='.repeat(50));
        console.log('To use this tool, you need to:');
        console.log('');
        console.log('1️⃣ Get Green API credentials:');
        console.log('   • Visit: https://green-api.com');
        console.log('   • Register for free account');
        console.log('   • Create new instance');
        console.log('   • Get instanceId and apiToken');
        console.log('');
        console.log('2️⃣ Link your WhatsApp number:');
        console.log('   • Scan QR code ONCE in Green API dashboard');
        console.log('   • Your number will be linked to the instance');
        console.log('');
        console.log('3️⃣ Configure this file:');
        console.log('   • Edit CONFIG section at top of file');
        console.log('   • Replace YOUR_INSTANCE_ID with your instance ID');
        console.log('   • Replace YOUR_API_TOKEN with your API token');
        console.log('   • Replace senderNumber with your WhatsApp number');
        console.log('');
        console.log('4️⃣ Run the program:');
        console.log('   • No more QR scanning needed!');
        console.log('   • Send messages from your number');
        console.log('');
        console.log('💡 Example configuration:');
        console.log('   instanceId: "1101123456"');
        console.log('   apiToken: "abc123def456..."');
        console.log('   senderNumber: "+201234567890"');
        console.log('='.repeat(50));
        
        setTimeout(() => {
            this.showMenu();
        }, 10000);
    }

    // Exit program
    exit() {
        console.log('\n👋 Thank you for using Simple WhatsApp API Sender!');
        this.rl.close();
        process.exit(0);
    }

    // Start the program
    start() {
        console.log('🚀 Simple WhatsApp API Message Sender');
        console.log('📡 Send messages from your number without QR scanning!');
        
        if (CONFIG.instanceId === 'YOUR_INSTANCE_ID' || CONFIG.apiToken === 'YOUR_API_TOKEN') {
            console.log('\n⚠️  API not configured yet!');
            console.log('📝 Please configure the API first (option 4)');
        } else {
            console.log('\n✅ API configured');
            console.log('📞 Sender number: ' + CONFIG.senderNumber);
        }
        
        this.showMenu();
    }
}

// Start the program
const sender = new SimpleWhatsAppSender();
sender.start();

// Handle program termination
process.on('SIGINT', () => {
    console.log('\n\n👋 Closing program...');
    process.exit(0);
});
