@echo off
title Simple Build - WhatsApp Sender Pro
color 0E

echo.
echo ==========================================
echo      Simple Build - WhatsApp Sender Pro
echo ==========================================
echo.

echo This is a simplified build process that avoids EBUSY errors.
echo.

echo [STEP 1] Pre-build cleanup...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1
timeout /t 2 >nul
echo ✅ Processes cleaned

echo.
echo [STEP 2] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found!
    echo Please install Node.js from: https://nodejs.org
    goto :end
)
echo ✅ Node.js ready

echo.
echo [STEP 3] Preparing package.json...
if not exist "package.json" (
    if exist "package-electron.json" (
        copy "package-electron.json" "package.json" >nul
        echo ✅ package.json created
    ) else (
        echo ❌ package-electron.json missing!
        goto :end
    )
)

echo.
echo [STEP 4] Installing only essential dependencies...
echo Installing electron-builder and electron...
echo This may take 3-5 minutes...
echo.

npm install electron electron-builder --save-dev --no-optional --force
if errorlevel 1 (
    echo.
    echo ❌ Installation failed with npm. Trying yarn...
    
    REM Try with yarn if available
    yarn add electron electron-builder --dev >nul 2>&1
    if errorlevel 1 (
        echo ❌ Both npm and yarn failed.
        echo.
        echo SOLUTIONS:
        echo 1. Run fix-all-problems.bat first
        echo 2. Restart computer and try again
        echo 3. Run as Administrator
        echo 4. Disable antivirus temporarily
        goto :end
    )
)

echo ✅ Essential dependencies installed

echo.
echo [STEP 5] Testing Electron...
npx electron --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Electron test failed
    goto :end
)
echo ✅ Electron is working

echo.
echo [STEP 6] Creating assets...
if not exist "assets" mkdir "assets"

REM Create a simple icon file
echo Creating simple icon...
echo. > "assets\icon.png"
echo ✅ Assets ready

echo.
echo [STEP 7] Testing the app...
echo Starting WhatsApp Sender Pro for testing...
echo Close the app window to continue...
echo.

start /wait npm start
echo ✅ App test completed

echo.
echo [STEP 8] Building the application...
echo.
echo ⚠️  IMPORTANT: This step takes 10-15 minutes!
echo    Please be patient and don't close this window.
echo.

echo Building Windows executable...
npx electron-builder --win --publish=never
if errorlevel 1 (
    echo.
    echo ❌ Full build failed. Trying portable build...
    
    npx electron-builder --win --dir
    if errorlevel 1 (
        echo ❌ Portable build also failed.
        echo.
        echo The app is working but build failed.
        echo You can still use: npm start
        goto :end
    )
)

echo.
echo ==========================================
echo          BUILD COMPLETED!
echo ==========================================
echo.

if exist "dist" (
    echo 📁 Output location: dist\
    echo.
    
    dir "dist" /b
    echo.
    
    if exist "dist\win-unpacked" (
        echo ✅ Portable version created!
        echo Location: dist\win-unpacked\WhatsApp Sender Pro.exe
    )
    
    if exist "dist\*.exe" (
        echo ✅ Installer created!
        for %%f in (dist\*.exe) do echo Location: %%f
    )
    
    echo.
    echo Opening output folder...
    start "" "dist"
    
    echo.
    echo 🎉 SUCCESS! Your WhatsApp Sender Pro is ready!
    echo.
    
    echo Would you like to test the built app? (y/n)
    set /p test_choice="Enter your choice: "
    
    if /i "%test_choice%"=="y" (
        if exist "dist\win-unpacked\WhatsApp Sender Pro.exe" (
            echo.
            echo Starting WhatsApp Sender Pro...
            start "" "dist\win-unpacked\WhatsApp Sender Pro.exe"
        ) else (
            echo ❌ Executable not found
        )
    )
    
) else (
    echo ❌ Build completed but no output found.
    echo.
    echo However, you can still run the app with:
    echo npm start
    echo.
    echo This will open the app in development mode.
)

:end
echo.
echo ==========================================
echo Press ANY KEY to close this window
echo ==========================================
pause
exit
