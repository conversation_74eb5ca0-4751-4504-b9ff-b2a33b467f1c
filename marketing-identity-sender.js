const axios = require('axios');
const readline = require('readline');

// Marketing Identity Configuration
const MARKETING_CONFIG = {
    // Your real WhatsApp number (required)
    realNumber: '+201234567890',  // Replace with your actual number
    
    // Marketing identity (legal and ethical)
    identity: {
        businessName: 'Digital Marketing Solutions',
        contactPerson: '<PERSON>',
        category: 'Marketing Services',
        description: 'Professional digital marketing services',
        website: 'www.marketing-solutions.com',
        email: '<EMAIL>'
    },
    
    // Message templates with professional signature
    templates: {
        intro: `Hello! I'm {contactPerson} from {businessName}.
We specialize in {category}.

{message}

---
Best regards,
{contactPerson}
{businessName}
📧 {email}
🌐 {website}

Note: This message was sent for marketing purposes. Reply STOP to unsubscribe.`,
        
        offer: `🎉 Special Offer from {businessName}!

{message}

This offer is valid for limited time only.

---
{contactPerson}
{businessName}
📞 Contact: {realNumber}
📧 Email: {email}

Reply STOP to unsubscribe from future offers.`,
        
        follow_up: `Hi! This is {contact<PERSON><PERSON>} from {businessName}.

{message}

Thank you for your interest in our services.

---
{businessName}
{category}
📧 {email}
🌐 {website}`
    },
    
    // API Configuration (Green API)
    api: {
        instanceId: 'YOUR_INSTANCE_ID',
        apiToken: 'YOUR_API_TOKEN',
        url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
    }
};

class MarketingIdentitySender {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        this.config = MARKETING_CONFIG;
    }

    // Format phone number
    formatPhoneNumber(phoneNumber) {
        let cleanNumber = phoneNumber.replace(/\D/g, '');
        
        if (!cleanNumber.startsWith('20') && cleanNumber.length === 11) {
            cleanNumber = '20' + cleanNumber;
        } else if (!cleanNumber.startsWith('20') && cleanNumber.length === 10) {
            cleanNumber = '20' + cleanNumber;
        }
        
        return cleanNumber + '@c.us';
    }

    // Create professional message with identity
    createProfessionalMessage(template, customMessage) {
        const identity = this.config.identity;
        
        return template
            .replace(/{contactPerson}/g, identity.contactPerson)
            .replace(/{businessName}/g, identity.businessName)
            .replace(/{category}/g, identity.category)
            .replace(/{description}/g, identity.description)
            .replace(/{website}/g, identity.website)
            .replace(/{email}/g, identity.email)
            .replace(/{realNumber}/g, this.config.realNumber)
            .replace(/{message}/g, customMessage);
    }

    // Send message with professional identity
    async sendMarketingMessage(toNumber, message, templateType = 'intro') {
        if (this.config.api.instanceId === 'YOUR_INSTANCE_ID') {
            console.log('❌ API not configured!');
            console.log('📝 Please configure your Green API credentials first');
            return false;
        }

        const template = this.config.templates[templateType];
        const professionalMessage = this.createProfessionalMessage(template, message);
        
        const formattedNumber = this.formatPhoneNumber(toNumber);
        const url = this.config.api.url
            .replace('{instanceId}', this.config.api.instanceId)
            .replace('{apiToken}', this.config.api.apiToken);
        
        const data = {
            chatId: formattedNumber,
            message: professionalMessage
        };

        console.log(`\n📤 Sending as: ${this.config.identity.contactPerson}`);
        console.log(`🏢 From: ${this.config.identity.businessName}`);
        console.log(`📱 To: ${toNumber}`);
        console.log(`📋 Template: ${templateType}`);
        console.log(`⏳ Processing...`);

        try {
            const response = await axios.post(url, data, {
                timeout: 30000,
                headers: { 'Content-Type': 'application/json' }
            });

            if (response.data && response.data.idMessage) {
                console.log('✅ Professional marketing message sent!');
                console.log(`📋 Message ID: ${response.data.idMessage}`);
                return true;
            } else {
                console.log('❌ Unexpected response:', response.data);
                return false;
            }
        } catch (error) {
            console.log('❌ Failed to send message');
            console.log('🔍 Error:', error.response?.data || error.message);
            return false;
        }
    }

    // Show main menu
    showMenu() {
        console.log('\n' + '='.repeat(60));
        console.log('📈 Professional Marketing Message Sender');
        console.log('🏢 Business: ' + this.config.identity.businessName);
        console.log('👤 Contact: ' + this.config.identity.contactPerson);
        console.log('='.repeat(60));
        console.log('1. Send introduction message');
        console.log('2. Send special offer message');
        console.log('3. Send follow-up message');
        console.log('4. Send bulk marketing messages');
        console.log('5. Configure business identity');
        console.log('6. Preview message templates');
        console.log('7. Exit');
        console.log('='.repeat(60));

        this.rl.question('Choose an option: ', (choice) => {
            this.handleMenuChoice(choice);
        });
    }

    // Handle menu choices
    handleMenuChoice(choice) {
        switch (choice) {
            case '1':
                this.sendIntroMessage();
                break;
            case '2':
                this.sendOfferMessage();
                break;
            case '3':
                this.sendFollowUpMessage();
                break;
            case '4':
                this.sendBulkMarketing();
                break;
            case '5':
                this.configureIdentity();
                break;
            case '6':
                this.previewTemplates();
                break;
            case '7':
                this.exit();
                break;
            default:
                console.log('❌ Invalid choice, please try again');
                this.showMenu();
        }
    }

    // Send introduction message
    sendIntroMessage() {
        console.log('\n📝 Send Introduction Message');
        console.log('-'.repeat(40));

        this.rl.question('Enter recipient number: ', (toNumber) => {
            if (!toNumber.trim()) {
                console.log('❌ Number required');
                this.showMenu();
                return;
            }

            this.rl.question('Enter your introduction message: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Message required');
                    this.showMenu();
                    return;
                }

                await this.sendMarketingMessage(toNumber, message, 'intro');
                setTimeout(() => this.showMenu(), 3000);
            });
        });
    }

    // Send offer message
    sendOfferMessage() {
        console.log('\n🎉 Send Special Offer Message');
        console.log('-'.repeat(40));

        this.rl.question('Enter recipient number: ', (toNumber) => {
            if (!toNumber.trim()) {
                console.log('❌ Number required');
                this.showMenu();
                return;
            }

            this.rl.question('Enter your offer details: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Offer details required');
                    this.showMenu();
                    return;
                }

                await this.sendMarketingMessage(toNumber, message, 'offer');
                setTimeout(() => this.showMenu(), 3000);
            });
        });
    }

    // Send follow-up message
    sendFollowUpMessage() {
        console.log('\n📞 Send Follow-up Message');
        console.log('-'.repeat(40));

        this.rl.question('Enter recipient number: ', (toNumber) => {
            if (!toNumber.trim()) {
                console.log('❌ Number required');
                this.showMenu();
                return;
            }

            this.rl.question('Enter follow-up message: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Message required');
                    this.showMenu();
                    return;
                }

                await this.sendMarketingMessage(toNumber, message, 'follow_up');
                setTimeout(() => this.showMenu(), 3000);
            });
        });
    }

    // Send bulk marketing messages
    sendBulkMarketing() {
        console.log('\n📊 Send Bulk Marketing Messages');
        console.log('-'.repeat(40));

        this.rl.question('Enter numbers (comma separated): ', (numbers) => {
            if (!numbers.trim()) {
                console.log('❌ Numbers required');
                this.showMenu();
                return;
            }

            const numberList = numbers.split(',').map(num => num.trim()).filter(num => num);

            console.log('Choose template:');
            console.log('1. Introduction');
            console.log('2. Special Offer');
            console.log('3. Follow-up');

            this.rl.question('Template choice (1-3): ', (templateChoice) => {
                const templates = ['intro', 'offer', 'follow_up'];
                const selectedTemplate = templates[parseInt(templateChoice) - 1];

                if (!selectedTemplate) {
                    console.log('❌ Invalid template choice');
                    this.showMenu();
                    return;
                }

                this.rl.question('Enter your message: ', async (message) => {
                    if (!message.trim()) {
                        console.log('❌ Message required');
                        this.showMenu();
                        return;
                    }

                    console.log('\n⏳ Sending bulk marketing messages...');
                    let successCount = 0;

                    for (let i = 0; i < numberList.length; i++) {
                        console.log(`\n📤 Sending ${i + 1}/${numberList.length} to: ${numberList[i]}`);
                        const success = await this.sendMarketingMessage(numberList[i], message, selectedTemplate);

                        if (success) successCount++;

                        if (i < numberList.length - 1) {
                            console.log('⏳ Waiting 5 seconds (professional spacing)...');
                            await new Promise(resolve => setTimeout(resolve, 5000));
                        }
                    }

                    console.log(`\n📊 Campaign Results: ${successCount}/${numberList.length} messages sent`);
                    setTimeout(() => this.showMenu(), 5000);
                });
            });
        });
    }

    // Configure business identity
    configureIdentity() {
        console.log('\n🏢 Configure Business Identity');
        console.log('-'.repeat(40));
        console.log('Current identity:');
        console.log(`Business: ${this.config.identity.businessName}`);
        console.log(`Contact: ${this.config.identity.contactPerson}`);
        console.log(`Category: ${this.config.identity.category}`);
        console.log(`Email: ${this.config.identity.email}`);
        console.log(`Website: ${this.config.identity.website}`);
        console.log('\n💡 Edit the MARKETING_CONFIG section in the code to change identity');
        
        setTimeout(() => this.showMenu(), 5000);
    }

    // Preview message templates
    previewTemplates() {
        console.log('\n📋 Message Templates Preview');
        console.log('='.repeat(60));
        
        Object.keys(this.config.templates).forEach(templateName => {
            console.log(`\n📝 ${templateName.toUpperCase()} Template:`);
            console.log('-'.repeat(30));
            const preview = this.createProfessionalMessage(
                this.config.templates[templateName], 
                '[Your custom message will appear here]'
            );
            console.log(preview);
            console.log('-'.repeat(30));
        });
        
        setTimeout(() => this.showMenu(), 10000);
    }

    // Exit program
    exit() {
        console.log('\n👋 Thank you for using Professional Marketing Sender!');
        console.log('📈 Remember to always respect recipients and follow marketing ethics');
        this.rl.close();
        process.exit(0);
    }

    // Start the program
    start() {
        console.log('🚀 Professional Marketing Message Sender');
        console.log('📈 Send marketing messages with professional identity');
        console.log('\n⚠️  Important: Configure your API and business identity first!');
        console.log('📝 Edit MARKETING_CONFIG section in the code');
        
        this.showMenu();
    }
}

// Start the program
const marketingSender = new MarketingIdentitySender();
marketingSender.start();

// Handle program termination
process.on('SIGINT', () => {
    console.log('\n\n👋 Closing program...');
    process.exit(0);
});
