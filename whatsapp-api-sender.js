const axios = require('axios');
const readline = require('readline');

// Configuration for different WhatsApp API services
const API_CONFIGS = {
    // Option 1: WhatsApp Business API (Official)
    whatsapp_business: {
        url: 'https://graph.facebook.com/v17.0/YOUR_PHONE_NUMBER_ID/messages',
        token: 'YOUR_ACCESS_TOKEN', // Get from Facebook Developer Console
        headers: {
            'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
            'Content-Type': 'application/json'
        }
    },
    
    // Option 2: Twilio WhatsApp API
    twilio: {
        url: 'https://api.twilio.com/2010-04-01/Accounts/YOUR_ACCOUNT_SID/Messages.json',
        accountSid: 'YOUR_ACCOUNT_SID',
        authToken: 'YOUR_AUTH_TOKEN',
        fromNumber: 'whatsapp:+***********' // Twilio WhatsApp number
    },
    
    // Option 3: ChatAPI.com
    chatapi: {
        url: 'https://api.chat-api.com/instance{instanceId}/sendMessage',
        instanceId: 'YOUR_INSTANCE_ID',
        token: 'YOUR_TOKEN'
    },
    
    // Option 4: Green API
    greenapi: {
        url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiTokenInstance}',
        instanceId: 'YOUR_INSTANCE_ID',
        apiToken: 'YOUR_API_TOKEN'
    }
};

class WhatsAppAPISender {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        // Set your preferred API service here
        this.currentAPI = 'greenapi'; // Change this to your preferred service
        this.senderNumber = ''; // Will be set by user
    }

    // Method 1: WhatsApp Business API (Official)
    async sendViaBusinessAPI(toNumber, message) {
        const config = API_CONFIGS.whatsapp_business;
        
        const data = {
            messaging_product: "whatsapp",
            to: toNumber,
            type: "text",
            text: {
                body: message
            }
        };

        try {
            const response = await axios.post(config.url, data, {
                headers: config.headers
            });
            return { success: true, data: response.data };
        } catch (error) {
            return { success: false, error: error.response?.data || error.message };
        }
    }

    // Method 2: Twilio WhatsApp API
    async sendViaTwilio(toNumber, message) {
        const config = API_CONFIGS.twilio;
        
        const data = new URLSearchParams({
            From: config.fromNumber,
            To: `whatsapp:${toNumber}`,
            Body: message
        });

        try {
            const response = await axios.post(config.url, data, {
                auth: {
                    username: config.accountSid,
                    password: config.authToken
                },
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });
            return { success: true, data: response.data };
        } catch (error) {
            return { success: false, error: error.response?.data || error.message };
        }
    }

    // Method 3: ChatAPI.com
    async sendViaChatAPI(toNumber, message) {
        const config = API_CONFIGS.chatapi;
        const url = config.url.replace('{instanceId}', config.instanceId);
        
        const data = {
            phone: toNumber,
            body: message
        };

        try {
            const response = await axios.post(`${url}?token=${config.token}`, data);
            return { success: true, data: response.data };
        } catch (error) {
            return { success: false, error: error.response?.data || error.message };
        }
    }

    // Method 4: Green API
    async sendViaGreenAPI(toNumber, message) {
        const config = API_CONFIGS.greenapi;
        const url = config.url
            .replace('{instanceId}', config.instanceId)
            .replace('{apiTokenInstance}', config.apiToken);
        
        const data = {
            chatId: `${toNumber}@c.us`,
            message: message
        };

        try {
            const response = await axios.post(url, data);
            return { success: true, data: response.data };
        } catch (error) {
            return { success: false, error: error.response?.data || error.message };
        }
    }

    // Format phone number
    formatPhoneNumber(phoneNumber) {
        let cleanNumber = phoneNumber.replace(/\D/g, '');
        
        // Add country code if not present
        if (!cleanNumber.startsWith('20') && cleanNumber.length === 11) {
            cleanNumber = '20' + cleanNumber;
        } else if (!cleanNumber.startsWith('20') && cleanNumber.length === 10) {
            cleanNumber = '20' + cleanNumber;
        }
        
        return '+' + cleanNumber;
    }

    // Send message using selected API
    async sendMessage(toNumber, message) {
        const formattedNumber = this.formatPhoneNumber(toNumber);
        
        console.log(`\n📤 Sending from: ${this.senderNumber}`);
        console.log(`📱 Sending to: ${formattedNumber}`);
        console.log(`💬 Message: ${message}`);
        console.log(`🔧 Using API: ${this.currentAPI}`);
        
        let result;
        
        switch (this.currentAPI) {
            case 'whatsapp_business':
                result = await this.sendViaBusinessAPI(formattedNumber, message);
                break;
            case 'twilio':
                result = await this.sendViaTwilio(formattedNumber, message);
                break;
            case 'chatapi':
                result = await this.sendViaChatAPI(formattedNumber, message);
                break;
            case 'greenapi':
                result = await this.sendViaGreenAPI(formattedNumber, message);
                break;
            default:
                result = { success: false, error: 'Unknown API service' };
        }

        if (result.success) {
            console.log('✅ Message sent successfully!');
            console.log('📋 Response:', JSON.stringify(result.data, null, 2));
        } else {
            console.log('❌ Failed to send message');
            console.log('🔍 Error:', result.error);
        }

        return result.success;
    }

    // Show main menu
    showMenu() {
        console.log('\n' + '='.repeat(60));
        console.log('📱 WhatsApp API Message Sender');
        console.log('📞 Sender Number: ' + (this.senderNumber || 'Not set'));
        console.log('🔧 Current API: ' + this.currentAPI);
        console.log('='.repeat(60));
        console.log('1. Set sender number');
        console.log('2. Configure API settings');
        console.log('3. Send single message');
        console.log('4. Send bulk messages');
        console.log('5. Test API connection');
        console.log('6. Exit');
        console.log('='.repeat(60));

        this.rl.question('Choose an option: ', (choice) => {
            this.handleMenuChoice(choice);
        });
    }

    // Handle menu choices
    handleMenuChoice(choice) {
        switch (choice) {
            case '1':
                this.setSenderNumber();
                break;
            case '2':
                this.configureAPI();
                break;
            case '3':
                this.sendSingleMessage();
                break;
            case '4':
                this.sendBulkMessages();
                break;
            case '5':
                this.testAPI();
                break;
            case '6':
                this.exit();
                break;
            default:
                console.log('❌ Invalid choice, please try again');
                this.showMenu();
        }
    }

    // Set sender number
    setSenderNumber() {
        console.log('\n📞 Set Sender Number');
        console.log('-'.repeat(30));
        
        this.rl.question('Enter your WhatsApp number (example: ***********): ', (number) => {
            if (!number.trim()) {
                console.log('❌ Number is required');
                this.showMenu();
                return;
            }
            
            this.senderNumber = this.formatPhoneNumber(number);
            console.log(`✅ Sender number set to: ${this.senderNumber}`);
            
            setTimeout(() => {
                this.showMenu();
            }, 2000);
        });
    }

    // Configure API
    configureAPI() {
        console.log('\n🔧 Configure API Service');
        console.log('-'.repeat(30));
        console.log('Available APIs:');
        console.log('1. WhatsApp Business API (Official)');
        console.log('2. Twilio WhatsApp API');
        console.log('3. ChatAPI.com');
        console.log('4. Green API');
        
        this.rl.question('Choose API service (1-4): ', (choice) => {
            const apis = ['whatsapp_business', 'twilio', 'chatapi', 'greenapi'];
            const selectedAPI = apis[parseInt(choice) - 1];
            
            if (selectedAPI) {
                this.currentAPI = selectedAPI;
                console.log(`✅ API set to: ${selectedAPI}`);
                console.log('\n⚠️  Remember to configure API credentials in the code!');
            } else {
                console.log('❌ Invalid choice');
            }
            
            setTimeout(() => {
                this.showMenu();
            }, 3000);
        });
    }

    // Send single message
    sendSingleMessage() {
        if (!this.senderNumber) {
            console.log('❌ Please set sender number first (option 1)');
            setTimeout(() => this.showMenu(), 2000);
            return;
        }

        console.log('\n📝 Send Single Message');
        console.log('-'.repeat(30));

        this.rl.question('Enter recipient number (example: ***********): ', (toNumber) => {
            if (!toNumber.trim()) {
                console.log('❌ Recipient number is required');
                this.showMenu();
                return;
            }

            this.rl.question('Enter message: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Message text is required');
                    this.showMenu();
                    return;
                }

                console.log('\n⏳ Sending message...');
                await this.sendMessage(toNumber, message);
                
                setTimeout(() => {
                    this.showMenu();
                }, 3000);
            });
        });
    }

    // Send bulk messages
    sendBulkMessages() {
        if (!this.senderNumber) {
            console.log('❌ Please set sender number first (option 1)');
            setTimeout(() => this.showMenu(), 2000);
            return;
        }

        console.log('\n📝 Send Bulk Messages');
        console.log('-'.repeat(30));

        this.rl.question('Enter numbers separated by comma: ', (numbers) => {
            if (!numbers.trim()) {
                console.log('❌ Numbers are required');
                this.showMenu();
                return;
            }

            const numberList = numbers.split(',').map(num => num.trim()).filter(num => num);
            
            this.rl.question('Enter message: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Message text is required');
                    this.showMenu();
                    return;
                }

                console.log('\n⏳ Sending bulk messages...');
                
                for (let i = 0; i < numberList.length; i++) {
                    console.log(`\n📤 Sending to ${i + 1}/${numberList.length}: ${numberList[i]}`);
                    await this.sendMessage(numberList[i], message);
                    
                    // Wait between messages
                    if (i < numberList.length - 1) {
                        console.log('⏳ Waiting 3 seconds...');
                        await new Promise(resolve => setTimeout(resolve, 3000));
                    }
                }

                console.log('\n✅ All messages processed!');
                
                setTimeout(() => {
                    this.showMenu();
                }, 3000);
            });
        });
    }

    // Test API connection
    async testAPI() {
        console.log('\n🔧 Testing API Connection...');
        console.log(`Using: ${this.currentAPI}`);
        
        // This would test the API connection
        console.log('⚠️  API test not implemented yet');
        console.log('💡 Configure your API credentials first');
        
        setTimeout(() => {
            this.showMenu();
        }, 3000);
    }

    // Exit program
    exit() {
        console.log('\n👋 Thank you for using WhatsApp API Sender!');
        this.rl.close();
        process.exit(0);
    }

    // Start the program
    start() {
        console.log('🚀 WhatsApp API Message Sender');
        console.log('📡 Send messages from any number without QR scanning!');
        console.log('\n⚠️  Important: You need to configure API credentials first');
        console.log('💡 Check the API_CONFIGS section in the code\n');
        
        this.showMenu();
    }
}

// Start the program
const apiSender = new WhatsAppAPISender();
apiSender.start();

// Handle program termination
process.on('SIGINT', () => {
    console.log('\n\n👋 Closing program...');
    process.exit(0);
});
