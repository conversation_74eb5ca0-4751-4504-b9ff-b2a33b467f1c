const { app, BrowserWindow, ipc<PERSON>ain, dialog, Menu } = require('electron');
const path = require('path');
const fs = require('fs');

// WhatsApp Web.js integration
const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const axios = require('axios');

let mainWindow;
let whatsappClient;
let isClientReady = false;

// Create main window
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 1000,
        minHeight: 700,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false
        },
        icon: path.join(__dirname, '../assets/icon.png'),
        titleBarStyle: 'default',
        show: false
    });

    // Load the HTML file
    mainWindow.loadFile(path.join(__dirname, '../src/index.html'));

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();

        // Focus on window
        if (process.platform === 'darwin') {
            app.dock.show();
        }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
        if (whatsappClient) {
            whatsappClient.destroy();
        }
    });

    // Create menu
    createMenu();
}

// Create application menu
function createMenu() {
    const template = [
        {
            label: 'File',
            submenu: [
                {
                    label: 'New Number Group',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('menu-new-group');
                    }
                },
                {
                    label: 'Import Numbers',
                    accelerator: 'CmdOrCtrl+I',
                    click: () => {
                        importNumbersFile();
                    }
                },
                { type: 'separator' },
                {
                    label: 'Exit',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'WhatsApp',
            submenu: [
                {
                    label: 'Connect QR Code',
                    accelerator: 'CmdOrCtrl+Q',
                    click: () => {
                        initializeWhatsAppClient();
                    }
                },
                {
                    label: 'Disconnect',
                    accelerator: 'CmdOrCtrl+D',
                    click: () => {
                        disconnectWhatsApp();
                    }
                },
                { type: 'separator' },
                {
                    label: 'API Settings',
                    click: () => {
                        mainWindow.webContents.send('show-api-settings');
                    }
                }
            ]
        },
        {
            label: 'Tools',
            submenu: [
                {
                    label: 'Message Templates',
                    click: () => {
                        mainWindow.webContents.send('show-templates');
                    }
                },
                {
                    label: 'Number Groups Manager',
                    click: () => {
                        mainWindow.webContents.send('show-groups-manager');
                    }
                },
                { type: 'separator' },
                {
                    label: 'Settings',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        mainWindow.webContents.send('show-settings');
                    }
                }
            ]
        },
        {
            label: 'Help',
            submenu: [
                {
                    label: 'About',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'About WhatsApp Sender Pro',
                            message: 'WhatsApp Sender Pro v1.0.0',
                            detail: 'Professional WhatsApp messaging tool with QR Code and API support.\n\nFeatures:\n• QR Code scanning\n• API integration\n• Number groups\n• Message templates\n• Bulk messaging\n• Professional interface'
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// Initialize WhatsApp Client
function initializeWhatsAppClient() {
    if (whatsappClient) {
        whatsappClient.destroy();
    }

    whatsappClient = new Client({
        authStrategy: new LocalAuth({
            dataPath: path.join(__dirname, '../.wwebjs_auth')
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-gpu'
            ]
        }
    });

    // QR Code event
    whatsappClient.on('qr', async (qr) => {
        try {
            const qrCodeDataURL = await qrcode.toDataURL(qr, {
                width: 300,
                margin: 2
            });
            mainWindow.webContents.send('qr-code', qrCodeDataURL);
        } catch (error) {
            console.error('QR Code generation error:', error);
        }
    });

    // Ready event
    whatsappClient.on('ready', () => {
        isClientReady = true;
        mainWindow.webContents.send('whatsapp-ready');
    });

    // Disconnected event
    whatsappClient.on('disconnected', (reason) => {
        isClientReady = false;
        mainWindow.webContents.send('whatsapp-disconnected', reason);
    });

    // Auth failure event
    whatsappClient.on('auth_failure', (msg) => {
        mainWindow.webContents.send('whatsapp-auth-failure', msg);
    });

    // Initialize client
    whatsappClient.initialize();
}

// Disconnect WhatsApp
function disconnectWhatsApp() {
    if (whatsappClient) {
        whatsappClient.destroy();
        whatsappClient = null;
        isClientReady = false;
        mainWindow.webContents.send('whatsapp-disconnected', 'Manual disconnect');
    }
}

// Import numbers file
async function importNumbersFile() {
    const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile'],
        filters: [
            { name: 'Text Files', extensions: ['txt'] },
            { name: 'CSV Files', extensions: ['csv'] },
            { name: 'All Files', extensions: ['*'] }
        ]
    });

    if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            mainWindow.webContents.send('numbers-imported', {
                filename: path.basename(filePath),
                content: content
            });
        } catch (error) {
            dialog.showErrorBox('Import Error', `Failed to import file: ${error.message}`);
        }
    }
}

// IPC Handlers
ipcMain.handle('send-message', async (event, data) => {
    const { phoneNumber, message, useAPI, apiConfig } = data;

    try {
        if (useAPI && apiConfig) {
            // Send via API
            return await sendViaAPI(phoneNumber, message, apiConfig);
        } else if (isClientReady && whatsappClient) {
            // Send via WhatsApp Web
            return await sendViaWhatsAppWeb(phoneNumber, message);
        } else {
            throw new Error('No connection method available');
        }
    } catch (error) {
        throw error;
    }
});

ipcMain.handle('send-bulk-messages', async (event, data) => {
    const { numbers, message, useAPI, apiConfig, delay = 3000 } = data;
    const results = [];

    for (let i = 0; i < numbers.length; i++) {
        try {
            mainWindow.webContents.send('bulk-progress', {
                current: i + 1,
                total: numbers.length,
                number: numbers[i]
            });

            let result;
            if (useAPI && apiConfig) {
                result = await sendViaAPI(numbers[i], message, apiConfig);
            } else if (isClientReady && whatsappClient) {
                result = await sendViaWhatsAppWeb(numbers[i], message);
            } else {
                throw new Error('No connection method available');
            }

            results.push({ number: numbers[i], success: true, result });

            // Delay between messages
            if (i < numbers.length - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        } catch (error) {
            results.push({ number: numbers[i], success: false, error: error.message });
        }
    }

    return results;
});

// Send via WhatsApp Web
async function sendViaWhatsAppWeb(phoneNumber, message) {
    const formattedNumber = formatPhoneNumber(phoneNumber);
    await whatsappClient.sendMessage(formattedNumber, message);
    return { method: 'whatsapp-web', success: true };
}

// Send via API
async function sendViaAPI(phoneNumber, message, apiConfig) {
    const formattedNumber = formatPhoneNumber(phoneNumber);
    const url = apiConfig.url
        .replace('{instanceId}', apiConfig.instanceId)
        .replace('{apiToken}', apiConfig.apiToken);

    const data = {
        chatId: formattedNumber,
        message: message
    };

    const response = await axios.post(url, data, {
        timeout: 30000,
        headers: { 'Content-Type': 'application/json' }
    });

    return { method: 'api', success: true, response: response.data };
}

// Format phone number
function formatPhoneNumber(phoneNumber) {
    let cleanNumber = phoneNumber.replace(/\D/g, '');

    if (!cleanNumber.startsWith('20') && cleanNumber.length === 11) {
        cleanNumber = '20' + cleanNumber;
    } else if (!cleanNumber.startsWith('20') && cleanNumber.length === 10) {
        cleanNumber = '20' + cleanNumber;
    }

    return cleanNumber + '@c.us';
}

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// Handle app quit
app.on('before-quit', () => {
    if (whatsappClient) {
        whatsappClient.destroy();
    }
});
