@echo off
title اختبار هاتف المرسل - WhatsApp Sender Pro
color 0C

echo.
echo ==========================================
echo    اختبار هاتف المرسل - WhatsApp Sender Pro
echo ==========================================
echo.

echo 🎉 تم إضافة ميزة هاتف المرسل الجديدة!
echo.

echo الآن لديك خيارين للاتصال:
echo.
echo 🔗 Green API:
echo    ✅ إرسال عبر API
echo    ❌ يحتاج اشتراك
echo    ✅ سريع ومباشر
echo.
echo 📱 هاتف المرسل (جديد!):
echo    ✅ ربط هاتفك مباشرة
echo    ✅ مجاني تماماً
echo    ✅ من رقمك الشخصي
echo    ✅ لا يحتاج API
echo.

echo جاري فتح التطبيق مع الميزة الجديدة...
echo.

REM فتح التطبيق المحدث
start "" "WhatsApp-Sender-Pro.html"

echo ✅ تم فتح WhatsApp Sender Pro!
echo.

echo كيفية اختبار هاتف المرسل:
echo.
echo 1️⃣ اذهب لتبويب "⚙️ إعدادات API"
echo 2️⃣ اختر "📱 هاتف المرسل" من الخيارات
echo 3️⃣ اضغط "📱 ربط الهاتف"
echo 4️⃣ امسح QR Code بهاتفك:
echo    • افتح WhatsApp في هاتفك
echo    • اذهب للإعدادات ← الأجهزة المرتبطة
echo    • اضغط "ربط جهاز"
echo    • امسح QR Code
echo 5️⃣ انتظر رسالة "تم الربط بنجاح"
echo 6️⃣ جرب إرسال رسالة تجريبية
echo.

echo مميزات هاتف المرسل:
echo ✅ مجاني 100%% - لا يحتاج اشتراك
echo ✅ رقمك الشخصي - المستقبل يرى رقمك
echo ✅ أمان عالي - اتصال مشفر
echo ✅ سهولة الربط - QR Code واحد فقط
echo ✅ جميع المميزات - إرسال فردي وجماعي
echo.

echo نصائح للاختبار:
echo 💡 تأكد من اتصال الإنترنت في الهاتف والكمبيوتر
echo 💡 لا تغلق WhatsApp في الهاتف أثناء الاختبار
echo 💡 يمكنك التبديل بين API وهاتف المرسل
echo 💡 الإعدادات محفوظة لكلا الطريقتين
echo 💡 جرب الإرسال لرقمك أولاً للاختبار
echo.

echo خطوات الاختبار المقترحة:
echo 1. ربط الهاتف عبر QR Code
echo 2. إرسال رسالة تجريبية لرقمك
echo 3. اختبار الإرسال الجماعي
echo 4. تجربة QR Code للإرسال السريع
echo 5. اختبار قطع وإعادة الاتصال
echo.

echo ==========================================
echo اضغط أي زر لإغلاق هذه النافذة
echo ==========================================
pause >nul
exit
