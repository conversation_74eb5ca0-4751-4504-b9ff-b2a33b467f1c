@echo off
title اختبار النسخة المحسنة - WhatsApp Sender Pro Enhanced
color 0A

echo.
echo ==========================================
echo    WhatsApp Sender Pro Enhanced - النسخة المحسنة
echo ==========================================
echo.

echo 🎉 تم إنشاء نسخة محسنة ومنظمة بالكامل!
echo.

echo المميزات الجديدة:
echo.
echo 🎨 تصميم منظم ومحترف:
echo ✅ قائمة جانبية للتنقل السهل
echo ✅ تصميم عصري وجذاب
echo ✅ ألوان WhatsApp الرسمية
echo ✅ واجهة سهلة الاستخدام
echo ✅ تجاوب مع جميع الشاشات
echo.
echo 📱 QR Code داخل الموقع:
echo ✅ مولد QR Code متقدم
echo ✅ أحجام مختلفة (200-500 بكسل)
echo ✅ تحميل وطباعة ومشاركة
echo ✅ يعمل مع WhatsApp فوراً
echo ✅ لا يحتاج مواقع خارجية
echo.
echo 📤 إرسال جماعي ذكي:
echo ✅ إرسال لعدة أرقام معاً
echo ✅ شريط تقدم مباشر
echo ✅ تحكم كامل (إيقاف/استئناف)
echo ✅ نتائج مفصلة لكل رقم
echo ✅ ترتيب عشوائي للأرقام
echo ✅ فحص صحة الأرقام
echo.
echo 📎 إرسال ملفات متقدم:
echo ✅ رفع ملفات متعددة
echo ✅ سحب وإفلات الملفات
echo ✅ توزيع ذكي للملفات
echo ✅ تحديد ملف لكل رقم
echo ✅ دعم جميع أنواع الملفات
echo ✅ حد أقصى 50MB لكل ملف
echo.
echo 🔗 طرق اتصال متعددة:
echo ✅ Green API للإرسال التلقائي
echo ✅ WhatsApp Web للإرسال اليدوي
echo ✅ تبديل سهل بين الطرق
echo ✅ فحص حالة الاتصال
echo ✅ حفظ الإعدادات تلقائياً
echo.

echo جاري فتح النسخة المحسنة...
echo.

REM فتح النسخة المحسنة
start "" "WhatsApp-Pro-Enhanced.html"

echo ✅ تم فتح WhatsApp Sender Pro Enhanced!
echo.

echo دليل الاستخدام السريع:
echo.

echo 📊 لوحة التحكم:
echo 1️⃣ افتح التطبيق
echo 2️⃣ ستجد لوحة التحكم مفتوحة
echo 3️⃣ راقب إحصائيات الإرسال
echo 4️⃣ تحقق من حالة الاتصال
echo 5️⃣ اضغط "🔍 فحص الاتصال" للتأكد
echo.

echo 🔗 إعداد الاتصال:
echo 1️⃣ اضغط "🔗 طرق الاتصال" في القائمة
echo 2️⃣ اختر بين Green API أو WhatsApp Web
echo 3️⃣ للـ API: أدخل Instance ID و Token
echo 4️⃣ للـ Web: اضغط "🌐 ربط WhatsApp Web"
echo 5️⃣ اضغط "🔍 اختبار الاتصال"
echo.

echo 📱 إرسال رسالة واحدة:
echo 1️⃣ اضغط "📱 رسالة واحدة"
echo 2️⃣ أدخل رقم الهاتف: 201234567890
echo 3️⃣ اكتب الرسالة
echo 4️⃣ أرفق ملف (اختياري)
echo 5️⃣ اختر قالب جاهز (اختياري)
echo 6️⃣ اضغط "📤 إرسال الرسالة"
echo.

echo 📤 الإرسال الجماعي:
echo 1️⃣ اضغط "📤 إرسال جماعي"
echo 2️⃣ أدخل الأرقام (رقم في كل سطر):
echo    201234567890
echo    201987654321
echo    201555666777
echo 3️⃣ اكتب الرسالة الجماعية
echo 4️⃣ حدد التأخير بين الرسائل
echo 5️⃣ فعل "ترتيب عشوائي" إذا أردت
echo 6️⃣ اضغط "✅ فحص الأرقام" للتأكد
echo 7️⃣ اضغط "🚀 بدء الإرسال الجماعي"
echo 8️⃣ راقب شريط التقدم والنتائج
echo.

echo 📎 إرسال الملفات:
echo 1️⃣ اضغط "📎 إرسال ملفات"
echo 2️⃣ اسحب الملفات أو اضغط "📁 رفع الملفات"
echo 3️⃣ اختر طريقة التوزيع:
echo    • نفس الملف للجميع
echo    • ملف مختلف لكل رقم
echo    • ملف عشوائي
echo    • تحديد يدوي
echo 4️⃣ أدخل أرقام الهواتف
echo 5️⃣ اكتب رسالة مرفقة (اختياري)
echo 6️⃣ اضغط "📤 إرسال الملفات"
echo.

echo 📱 QR Code المتقدم:
echo 1️⃣ اضغط "📱 QR Code"
echo 2️⃣ أدخل رقم الهاتف
echo 3️⃣ اكتب الرسالة
echo 4️⃣ اختر حجم QR Code
echo 5️⃣ اضغط "🔄 إنشاء QR Code"
echo 6️⃣ امسح QR Code بهاتفك
echo 7️⃣ استخدم أزرار التحميل والطباعة
echo.

echo المميزات المتقدمة:
echo.

echo 🎯 الإرسال الجماعي الذكي:
echo ✅ فحص صحة الأرقام قبل الإرسال
echo ✅ ترتيب عشوائي لتجنب الحظر
echo ✅ تأخير قابل للتخصيص
echo ✅ إيقاف واستئناف الإرسال
echo ✅ نتائج مفصلة لكل رقم
echo ✅ عداد النجاح والفشل
echo.

echo 📁 إدارة الملفات الذكية:
echo ✅ رفع ملفات متعددة معاً
echo ✅ سحب وإفلات مباشر
echo ✅ معاينة الملفات المرفوعة
echo ✅ حذف ملفات فردية
echo ✅ توزيع تلقائي أو يدوي
echo ✅ دعم الصور والفيديو والمستندات
echo.

echo 🎨 واجهة محسنة:
echo ✅ قائمة جانبية منظمة
echo ✅ تصميم عصري وجذاب
echo ✅ ألوان WhatsApp الرسمية
echo ✅ أيقونات واضحة ومفهومة
echo ✅ تجاوب مع الهواتف والأجهزة اللوحية
echo ✅ رسائل تنبيه واضحة
echo.

echo 📊 لوحة تحكم متقدمة:
echo ✅ إحصائيات مباشرة
echo ✅ حالة الاتصال الحالية
echo ✅ عداد الرسائل المرسلة
echo ✅ عداد الرسائل الفاشلة
echo ✅ فحص سريع للاتصال
echo.

echo نصائح للاستخدام الأمثل:
echo.

echo 💡 للإرسال الجماعي:
echo • ابدأ بعدد قليل من الأرقام للاختبار
echo • استخدم تأخير 3-5 ثوانٍ بين الرسائل
echo • فعل الترتيب العشوائي لتجنب الحظر
echo • راقب النتائج وأوقف الإرسال عند الحاجة
echo.

echo 💡 لإرسال الملفات:
echo • تأكد من حجم الملفات (أقل من 50MB)
echo • استخدم أسماء ملفات واضحة
echo • اختبر مع ملف واحد أولاً
echo • استخدم التوزيع اليدوي للتحكم الدقيق
echo.

echo 💡 لـ QR Code:
echo • استخدم حجم متوسط (300x300) للاستخدام العام
echo • استخدم حجم كبير (500x500) للطباعة
echo • احفظ QR Code للاستخدام المتكرر
echo • اختبر QR Code قبل المشاركة
echo.

echo علامات النجاح:
echo.
echo ✅ الاتصال:
echo • ظهور "✅ متصل عبر API" أو "🌐 متصل عبر WhatsApp Web"
echo • نجاح اختبار الاتصال
echo • تحديث لوحة التحكم
echo.
echo ✅ الإرسال:
echo • ظهور "✅ تم الإرسال بنجاح"
echo • تحديث عداد الرسائل المرسلة
echo • ظهور النتائج في قائمة الإرسال الجماعي
echo.
echo ✅ QR Code:
echo • ظهور QR Code في المنطقة المخصصة
echo • إمكانية مسح QR Code بالهاتف
echo • فتح WhatsApp مع الرسالة جاهزة
echo.

echo مشاكل محتملة وحلولها:
echo.
echo ❌ إذا لم يعمل الاتصال:
echo • تحقق من بيانات API
echo • تأكد من ربط WhatsApp في لوحة Green API
echo • جرب WhatsApp Web كبديل
echo.
echo ❌ إذا فشل الإرسال الجماعي:
echo • تحقق من صحة الأرقام
echo • قلل التأخير بين الرسائل
echo • جرب أرقام قليلة أولاً
echo.
echo ❌ إذا لم تعمل الملفات:
echo • تحقق من حجم الملفات
echo • تأكد من نوع الملف المدعوم
echo • جرب ملف واحد أولاً
echo.

echo ==========================================
echo استمتع بالنسخة المحسنة من WhatsApp Sender Pro!
echo ==========================================
echo.
echo اضغط أي زر لإغلاق هذه النافذة
pause >nul
exit
