@echo off
title WhatsApp API Sender

echo ========================================
echo    WhatsApp API Message Sender
echo ========================================
echo.
echo Send messages from your own number
echo without QR scanning every time!
echo.

echo Checking requirements...
echo.

echo 1. Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not installed!
    echo Please install Node.js from https://nodejs.org
    pause
    exit
)
echo ✅ Node.js is installed

echo.
echo 2. Checking axios dependency...
npm list axios >nul 2>&1
if errorlevel 1 (
    echo ⏳ Installing axios...
    npm install axios
    if errorlevel 1 (
        echo ❌ Failed to install axios
        pause
        exit
    )
)
echo ✅ axios is available

echo.
echo 3. Starting WhatsApp API Sender...
echo.
echo ⚠️  IMPORTANT: Make sure you have configured the API first!
echo 📝 Edit simple-api-sender.js and add your credentials
echo 💡 See API_SETUP_GUIDE.md for instructions
echo.

node simple-api-sender.js

echo.
echo Program ended. Press any key to exit...
pause >nul
