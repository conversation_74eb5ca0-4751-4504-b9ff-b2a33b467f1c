2025/05/26-20:02:34.590 19f0 Reusing MANIFEST E:\my project program\update\whats app hacker\.wwebjs_auth\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/05/26-20:02:34.591 19f0 Recovering log #207
2025/05/26-20:02:34.717 19f0 Reusing old log E:\my project program\update\whats app hacker\.wwebjs_auth\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/000207.log 
2025/05/26-20:02:36.170 32cc Level-0 table #213: started
2025/05/26-20:02:36.347 32cc Level-0 table #213: 141669 bytes OK
2025/05/26-20:02:36.421 32cc Delete type=0 #207
2025/05/26-20:02:36.422 32cc Manual compaction at level-0 from '\x00Q\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00R\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/26-20:02:40.628 1d14 Compacting 1@1 + 1@2 files
2025/05/26-20:02:41.289 1d14 Generated table #214@1: 30038 keys, 1487693 bytes
2025/05/26-20:02:41.289 1d14 Compacted 1@1 + 1@2 files => 1487693 bytes
2025/05/26-20:02:41.392 1d14 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/26-20:02:41.393 1d14 Delete type=2 #210
2025/05/26-20:02:41.393 1d14 Delete type=2 #213
