// Check if we're in Electron environment
let ipcRenderer, fs, path;
try {
    const electron = require('electron');
    ipc<PERSON><PERSON>er = electron.ipcRenderer;
    fs = require('fs');
    path = require('path');
} catch (error) {
    console.log('Running in browser mode - some features disabled');
}

// Application State
let appState = {
    isConnected: false,
    connectionMethod: 'qr',
    currentTab: 'dashboard',
    selectedGroups: [],
    messagesSent: 0,
    successRate: 100,
    settings: {
        apiInstanceId: '',
        apiToken: '',
        businessName: '',
        contactPerson: '',
        businessEmail: '',
        businessWebsite: '',
        autoSave: true,
        confirmSend: true,
        soundNotifications: true
    }
};

// DOM Elements
const elements = {
    statusIndicator: document.getElementById('status-indicator'),
    statusText: document.getElementById('status-text'),
    connectBtn: document.getElementById('connect-btn'),
    qrCard: document.getElementById('qr-card'),
    qrContainer: document.getElementById('qr-container'),
    messagesSentStat: document.getElementById('messages-sent'),
    successRateStat: document.getElementById('success-rate'),
    totalGroupsStat: document.getElementById('total-groups')
};

// Initialize Application
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
    setupEventListeners();
    loadSettings();
    loadNumberGroups();
    updateStatistics();
});

// Initialize App
function initializeApp() {
    // Set initial tab
    showTab('dashboard');

    // Update connection status
    updateConnectionStatus('disconnected');

    // Load saved data
    loadSettings();

    console.log('WhatsApp Sender Pro initialized');
}

// Setup Event Listeners
function setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', (e) => {
            const tab = e.currentTarget.dataset.tab;
            showTab(tab);
        });
    });

    // Quick actions
    document.querySelectorAll('.quick-action-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const action = e.currentTarget.dataset.action;
            showTab(action);
        });
    });

    // Connection
    elements.connectBtn.addEventListener('click', handleConnect);

    // Connection method change
    document.querySelectorAll('input[name="connection-method"]').forEach(radio => {
        radio.addEventListener('change', (e) => {
            appState.connectionMethod = e.target.value;
            updateConnectionUI();
        });
    });

    // Single message
    document.getElementById('send-single').addEventListener('click', sendSingleMessage);
    document.getElementById('preview-single').addEventListener('click', previewSingleMessage);

    // Bulk messages
    document.getElementById('send-bulk').addEventListener('click', sendBulkMessages);
    document.getElementById('preview-bulk').addEventListener('click', previewBulkMessage);

    // Recipient options
    document.querySelectorAll('.option-tab').forEach(tab => {
        tab.addEventListener('click', (e) => {
            const option = e.currentTarget.dataset.option;
            switchRecipientOption(option);
        });
    });

    // Import file
    document.getElementById('import-file').addEventListener('click', importNumbersFile);

    // Settings
    document.getElementById('save-settings').addEventListener('click', saveSettings);
    document.getElementById('reset-settings').addEventListener('click', resetSettings);
    document.getElementById('test-api').addEventListener('click', testAPIConnection);

    // Groups and templates
    document.getElementById('create-group').addEventListener('click', createNumberGroup);
    document.getElementById('create-template').addEventListener('click', createMessageTemplate);

    // Character count
    document.getElementById('single-message').addEventListener('input', updateCharCount);
    document.getElementById('bulk-message-text').addEventListener('input', updateCharCount);
}

// Show Tab
function showTab(tabName) {
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.tab === tabName) {
            item.classList.add('active');
        }
    });

    // Update content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    const targetTab = document.getElementById(tabName);
    if (targetTab) {
        targetTab.classList.add('active');
        appState.currentTab = tabName;

        // Load tab-specific data
        if (tabName === 'number-groups') {
            loadNumberGroups();
        } else if (tabName === 'templates') {
            loadMessageTemplates();
        }
    }
}

// Handle Connection
function handleConnect() {
    if (appState.isConnected) {
        disconnect();
    } else {
        connect();
    }
}

// Connect
function connect() {
    if (appState.connectionMethod === 'qr') {
        connectViaQR();
    } else {
        connectViaAPI();
    }
}

// Connect via QR
function connectViaQR() {
    updateConnectionStatus('connecting');
    elements.qrCard.style.display = 'block';

    // Initialize WhatsApp client via IPC
    if (ipcRenderer) {
        ipcRenderer.send('initialize-whatsapp');
        showToast('Initializing WhatsApp connection...', 'info');
    } else {
        showToast('QR Code feature requires desktop app', 'warning');
        updateConnectionStatus('disconnected');
    }
}

// Connect via API
function connectViaAPI() {
    const instanceId = document.getElementById('api-instance-id').value;
    const apiToken = document.getElementById('api-token').value;

    if (!instanceId || !apiToken) {
        showToast('Please configure API settings first', 'error');
        showTab('settings');
        return;
    }

    updateConnectionStatus('connecting');

    // Test API connection
    testAPIConnection().then(success => {
        if (success) {
            updateConnectionStatus('connected');
            appState.isConnected = true;
            showToast('Connected via API successfully!', 'success');
        } else {
            updateConnectionStatus('disconnected');
            showToast('API connection failed', 'error');
        }
    });
}

// Disconnect
function disconnect() {
    updateConnectionStatus('disconnected');
    appState.isConnected = false;
    elements.qrCard.style.display = 'none';

    // Send disconnect signal
    ipcRenderer.send('disconnect-whatsapp');

    showToast('Disconnected', 'info');
}

// Update Connection Status
function updateConnectionStatus(status) {
    elements.statusIndicator.className = `status-${status}`;

    switch (status) {
        case 'connected':
            elements.statusText.textContent = 'Connected';
            elements.connectBtn.innerHTML = '<i class="fas fa-stop"></i> Disconnect';
            elements.connectBtn.className = 'btn btn-danger';
            appState.isConnected = true;
            break;
        case 'connecting':
            elements.statusText.textContent = 'Connecting...';
            elements.connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting';
            elements.connectBtn.className = 'btn btn-secondary';
            elements.connectBtn.disabled = true;
            break;
        case 'disconnected':
        default:
            elements.statusText.textContent = 'Disconnected';
            elements.connectBtn.innerHTML = '<i class="fas fa-play"></i> Connect';
            elements.connectBtn.className = 'btn btn-primary';
            elements.connectBtn.disabled = false;
            appState.isConnected = false;
            break;
    }
}

// Update Connection UI
function updateConnectionUI() {
    if (appState.connectionMethod === 'qr') {
        elements.qrCard.style.display = appState.isConnected ? 'block' : 'none';
    } else {
        elements.qrCard.style.display = 'none';
    }
}

// Send Single Message
async function sendSingleMessage() {
    const phoneNumber = document.getElementById('single-phone').value.trim();
    const message = document.getElementById('single-message').value.trim();

    if (!phoneNumber || !message) {
        showToast('Please enter phone number and message', 'error');
        return;
    }

    if (!appState.isConnected) {
        showToast('Please connect first', 'error');
        return;
    }

    try {
        const useAPI = appState.connectionMethod === 'api';
        const apiConfig = useAPI ? {
            instanceId: appState.settings.apiInstanceId,
            apiToken: appState.settings.apiToken,
            url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
        } : null;

        showToast('Sending message...', 'info');

        if (ipcRenderer) {
            const result = await ipcRenderer.invoke('send-message', {
                phoneNumber,
                message,
                useAPI,
                apiConfig
            });

            showToast('Message sent successfully!', 'success');
            appState.messagesSent++;
            updateStatistics();

            // Clear form
            document.getElementById('single-phone').value = '';
            document.getElementById('single-message').value = '';
        } else {
            // Demo mode for browser testing
            showToast('Demo mode: Message would be sent to ' + phoneNumber, 'info');
            appState.messagesSent++;
            updateStatistics();
        }

    } catch (error) {
        showToast(`Failed to send message: ${error.message}`, 'error');
    }
}

// Preview Single Message
function previewSingleMessage() {
    const phoneNumber = document.getElementById('single-phone').value.trim();
    const message = document.getElementById('single-message').value.trim();

    if (!phoneNumber || !message) {
        showToast('Please enter phone number and message', 'error');
        return;
    }

    const previewContent = `
        <h3>Message Preview</h3>
        <div class="preview-container">
            <p><strong>To:</strong> ${phoneNumber}</p>
            <p><strong>Message:</strong></p>
            <div class="message-preview">${message}</div>
        </div>
    `;

    showModal(previewContent);
}

// Send Bulk Messages
async function sendBulkMessages() {
    const numbers = getBulkNumbers();
    const message = document.getElementById('bulk-message-text').value.trim();
    const delay = parseInt(document.getElementById('delay-between').value) * 1000;

    if (numbers.length === 0) {
        showToast('Please add phone numbers', 'error');
        return;
    }

    if (!message) {
        showToast('Please enter message', 'error');
        return;
    }

    if (!appState.isConnected) {
        showToast('Please connect first', 'error');
        return;
    }

    try {
        const useAPI = appState.connectionMethod === 'api';
        const apiConfig = useAPI ? {
            instanceId: appState.settings.apiInstanceId,
            apiToken: appState.settings.apiToken,
            url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
        } : null;

        // Show progress
        const progressContainer = document.getElementById('bulk-progress');
        progressContainer.style.display = 'block';

        showToast(`Starting bulk send to ${numbers.length} numbers...`, 'info');

        const results = await ipcRenderer.invoke('send-bulk-messages', {
            numbers,
            message,
            useAPI,
            apiConfig,
            delay
        });

        // Hide progress
        progressContainer.style.display = 'none';

        // Show results
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        showToast(`Bulk send completed: ${successful} successful, ${failed} failed`, 'success');

        appState.messagesSent += successful;
        updateStatistics();

    } catch (error) {
        document.getElementById('bulk-progress').style.display = 'none';
        showToast(`Bulk send failed: ${error.message}`, 'error');
    }
}

// Get Bulk Numbers
function getBulkNumbers() {
    const activeOption = document.querySelector('.option-tab.active').dataset.option;
    let numbers = [];

    if (activeOption === 'manual') {
        const text = document.getElementById('bulk-numbers').value;
        numbers = text.split('\n')
            .map(line => line.trim())
            .filter(line => line && /^\d+$/.test(line));
    } else if (activeOption === 'groups') {
        // Get selected groups
        const selectedGroups = document.querySelectorAll('.group-item.selected');
        selectedGroups.forEach(group => {
            const groupName = group.dataset.groupName;
            const groupNumbers = loadGroupNumbers(groupName);
            numbers = numbers.concat(groupNumbers);
        });
    }

    return [...new Set(numbers)]; // Remove duplicates
}

// Switch Recipient Option
function switchRecipientOption(option) {
    // Update tabs
    document.querySelectorAll('.option-tab').forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.option === option) {
            tab.classList.add('active');
        }
    });

    // Update content
    document.querySelectorAll('.recipient-content').forEach(content => {
        content.classList.remove('active');
    });

    document.getElementById(`${option}-entry`).classList.add('active');

    if (option === 'groups') {
        loadNumberGroups();
    }
}

// Load Number Groups
function loadNumberGroups() {
    const groupsContainer = document.getElementById('groups-list');
    const groupsGrid = document.getElementById('groups-container');

    if (!groupsContainer && !groupsGrid) return;

    try {
        const groupsPath = path.join(__dirname, '../numbers-groups');

        if (!fs.existsSync(groupsPath)) {
            fs.mkdirSync(groupsPath, { recursive: true });
        }

        const files = fs.readdirSync(groupsPath)
            .filter(file => file.endsWith('.txt'))
            .map(file => {
                const groupName = file.replace('.txt', '');
                const numbers = loadGroupNumbers(groupName);
                return { name: groupName, count: numbers.length };
            });

        // Update bulk message groups list
        if (groupsContainer) {
            groupsContainer.innerHTML = files.map(group => `
                <div class="group-item" data-group-name="${group.name}">
                    <div class="group-info">
                        <h4>${group.name}</h4>
                        <p>${group.count} numbers</p>
                    </div>
                    <div class="group-count">${group.count}</div>
                </div>
            `).join('');

            // Add click handlers
            groupsContainer.querySelectorAll('.group-item').forEach(item => {
                item.addEventListener('click', () => {
                    item.classList.toggle('selected');
                });
            });
        }

        // Update groups management grid
        if (groupsGrid) {
            groupsGrid.innerHTML = files.map(group => `
                <div class="group-card">
                    <h3>${group.name}</h3>
                    <p>${group.count} numbers</p>
                    <div class="group-actions">
                        <button class="btn btn-secondary" onclick="editGroup('${group.name}')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-danger" onclick="deleteGroup('${group.name}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Update statistics
        elements.totalGroupsStat.textContent = files.length;

    } catch (error) {
        console.error('Error loading number groups:', error);
    }
}

// Load Group Numbers
function loadGroupNumbers(groupName) {
    try {
        const filePath = path.join(__dirname, '../numbers-groups', `${groupName}.txt`);
        if (!fs.existsSync(filePath)) return [];

        const content = fs.readFileSync(filePath, 'utf8');
        return content.split('\n')
            .map(line => line.trim())
            .filter(line => line && !line.startsWith('#') && /^\d+$/.test(line));
    } catch (error) {
        console.error('Error loading group numbers:', error);
        return [];
    }
}

// Update Statistics
function updateStatistics() {
    elements.messagesSentStat.textContent = appState.messagesSent;
    elements.successRateStat.textContent = `${appState.successRate}%`;
}

// Show Toast Notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;

    document.getElementById('toast-container').appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// Get Toast Icon
function getToastIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

// Show Modal
function showModal(content) {
    const overlay = document.getElementById('modal-overlay');
    const modalContent = document.getElementById('modal-content');

    modalContent.innerHTML = content + `
        <div class="modal-actions">
            <button class="btn btn-secondary" onclick="closeModal()">Close</button>
        </div>
    `;

    overlay.classList.add('active');
}

// Close Modal
function closeModal() {
    document.getElementById('modal-overlay').classList.remove('active');
}

// Update Character Count
function updateCharCount(e) {
    const textarea = e.target;
    const count = textarea.value.length;
    const counter = textarea.parentElement.querySelector('.char-count');
    if (counter) {
        counter.textContent = `${count} characters`;
    }
}

// Load Settings
function loadSettings() {
    // Load from localStorage or default values
    const saved = localStorage.getItem('whatsapp-sender-settings');
    if (saved) {
        appState.settings = { ...appState.settings, ...JSON.parse(saved) };
    }

    // Update UI
    document.getElementById('api-instance-id').value = appState.settings.apiInstanceId;
    document.getElementById('api-token').value = appState.settings.apiToken;
    document.getElementById('business-name').value = appState.settings.businessName;
    document.getElementById('contact-person').value = appState.settings.contactPerson;
    document.getElementById('business-email').value = appState.settings.businessEmail;
    document.getElementById('business-website').value = appState.settings.businessWebsite;
    document.getElementById('auto-save').checked = appState.settings.autoSave;
    document.getElementById('confirm-send').checked = appState.settings.confirmSend;
    document.getElementById('sound-notifications').checked = appState.settings.soundNotifications;
}

// Save Settings
function saveSettings() {
    appState.settings = {
        apiInstanceId: document.getElementById('api-instance-id').value,
        apiToken: document.getElementById('api-token').value,
        businessName: document.getElementById('business-name').value,
        contactPerson: document.getElementById('contact-person').value,
        businessEmail: document.getElementById('business-email').value,
        businessWebsite: document.getElementById('business-website').value,
        autoSave: document.getElementById('auto-save').checked,
        confirmSend: document.getElementById('confirm-send').checked,
        soundNotifications: document.getElementById('sound-notifications').checked
    };

    localStorage.setItem('whatsapp-sender-settings', JSON.stringify(appState.settings));
    showToast('Settings saved successfully!', 'success');
}

// Reset Settings
function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default?')) {
        localStorage.removeItem('whatsapp-sender-settings');
        appState.settings = {
            apiInstanceId: '',
            apiToken: '',
            businessName: '',
            contactPerson: '',
            businessEmail: '',
            businessWebsite: '',
            autoSave: true,
            confirmSend: true,
            soundNotifications: true
        };
        loadSettings();
        showToast('Settings reset to default', 'info');
    }
}

// Test API Connection
async function testAPIConnection() {
    const instanceId = document.getElementById('api-instance-id').value;
    const apiToken = document.getElementById('api-token').value;

    if (!instanceId || !apiToken) {
        showToast('Please enter Instance ID and API Token', 'error');
        return false;
    }

    try {
        const url = `https://api.green-api.com/waInstance${instanceId}/getSettings/${apiToken}`;
        const response = await fetch(url);

        if (response.ok) {
            showToast('API connection successful!', 'success');
            return true;
        } else {
            showToast('API connection failed', 'error');
            return false;
        }
    } catch (error) {
        showToast(`API test failed: ${error.message}`, 'error');
        return false;
    }
}

// IPC Event Listeners
ipcRenderer.on('qr-code', (event, qrDataURL) => {
    const qrContainer = document.getElementById('qr-container');
    qrContainer.innerHTML = `<img id="qr-image" src="${qrDataURL}" alt="QR Code">`;
    showToast('QR Code generated. Please scan with your phone.', 'info');
});

ipcRenderer.on('whatsapp-ready', () => {
    updateConnectionStatus('connected');
    elements.qrCard.style.display = 'none';
    showToast('WhatsApp connected successfully!', 'success');
});

ipcRenderer.on('whatsapp-disconnected', (event, reason) => {
    updateConnectionStatus('disconnected');
    elements.qrCard.style.display = 'none';
    showToast(`WhatsApp disconnected: ${reason}`, 'warning');
});

ipcRenderer.on('whatsapp-auth-failure', (event, message) => {
    updateConnectionStatus('disconnected');
    showToast(`Authentication failed: ${message}`, 'error');
});

ipcRenderer.on('bulk-progress', (event, data) => {
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    const currentNumber = document.getElementById('current-number');

    const percentage = (data.current / data.total) * 100;
    progressFill.style.width = `${percentage}%`;
    progressText.textContent = `${data.current}/${data.total}`;
    currentNumber.textContent = `Sending to: ${data.number}`;
});

// Close modal when clicking outside
document.getElementById('modal-overlay').addEventListener('click', (e) => {
    if (e.target === e.currentTarget) {
        closeModal();
    }
});

// Placeholder functions for future implementation
function createNumberGroup() {
    showToast('Create Number Group feature coming soon!', 'info');
}

function createMessageTemplate() {
    showToast('Create Message Template feature coming soon!', 'info');
}

function loadMessageTemplates() {
    // Placeholder for loading message templates
}

function editGroup(groupName) {
    showToast(`Edit group: ${groupName} - Feature coming soon!`, 'info');
}

function deleteGroup(groupName) {
    if (confirm(`Are you sure you want to delete the group "${groupName}"?`)) {
        try {
            const filePath = path.join(__dirname, '../numbers-groups', `${groupName}.txt`);
            fs.unlinkSync(filePath);
            loadNumberGroups();
            showToast(`Group "${groupName}" deleted successfully!`, 'success');
        } catch (error) {
            showToast(`Failed to delete group: ${error.message}`, 'error');
        }
    }
}

function importNumbersFile() {
    ipcRenderer.send('import-numbers-file');
}

ipcRenderer.on('numbers-imported', (event, data) => {
    const textarea = document.getElementById('bulk-numbers');
    const numbers = data.content.split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('#') && /^\d+$/.test(line));

    textarea.value = numbers.join('\n');
    showToast(`Imported ${numbers.length} numbers from ${data.filename}`, 'success');
});
