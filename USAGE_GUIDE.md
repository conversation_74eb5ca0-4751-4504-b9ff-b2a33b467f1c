# 🚀 Quick Usage Guide

## Problem Fixed! ✅

The encoding issue with Arabic text in `.bat` files has been resolved. All files now use English to avoid command line errors.

## 📋 Available Run Methods

### 1️⃣ **Simplest Method** - `run.bat`
```cmd
run.bat
```
- Direct execution without menus
- Works immediately

### 2️⃣ **Interactive Method** - `start.bat`
```cmd
start.bat
```
- Interactive menu in English
- Multiple options (Interactive, Quick, Test, Exit)

### 3️⃣ **Direct Command**
```cmd
node whatsapp-sender.js
```
- Run directly without batch files

### 4️⃣ **Quick Send Method**
```cmd
node quick-send.js
```
- Pre-configured sending (edit config first)

### 5️⃣ **Test Libraries**
```cmd
node test-simple.js
```
- Check if all libraries are working

## 🔧 If Nothing Works

### Step 1: Check Node.js
```cmd
node --version
```
Should show version number (like v18.x.x)

### Step 2: Install Dependencies
```cmd
npm install
```

### Step 3: Test Libraries
```cmd
node test-simple.js
```
Should show all green checkmarks ✅

### Step 4: Run Program
```cmd
node whatsapp-sender.js
```

## 📱 How to Use

1. **Run the program** using any method above
2. **Wait for QR Code** (may take 1-2 minutes first time)
3. **Scan QR Code** with your phone:
   - Open WhatsApp
   - Go to Settings > Linked Devices
   - Tap "Link a Device"
   - Scan the code
4. **Send messages** through the menu

## 📝 Example Usage

### Single Message:
```
Phone: 01234567890
Message: Hello! This is a test message.
```

### Multiple Messages:
```
Phones: 01234567890,01987654321,01555666777
Message: Important announcement for everyone!
```

## ⚠️ Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| `'node' is not recognized` | Install Node.js from nodejs.org |
| `Cannot find module` | Run `npm install` |
| QR Code not showing | Wait longer, restart program |
| Message failed | Check number format, internet |
| Encoding errors | Use English interface only |

## 🎯 Recommended Order to Try

1. **`run.bat`** - Simplest
2. **`node whatsapp-sender.js`** - Direct
3. **`start.bat`** - With menu
4. **`node test-simple.js`** - If problems

## 💡 Tips

- **First run takes longer** (downloading browser)
- **Keep internet stable** during operation
- **Don't close terminal** while sending
- **Wait for "Login successful"** message
- **Use valid phone numbers** only

---

**The tool is now fully in English and should work without encoding issues!**
