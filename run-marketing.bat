@echo off
title Professional Marketing Sender

echo ========================================
echo    Professional Marketing Sender
echo ========================================
echo.
echo Send professional marketing messages
echo with your business identity!
echo.

echo Checking requirements...
echo.

echo 1. Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not installed!
    echo Please install Node.js from https://nodejs.org
    pause
    exit
)
echo ✅ Node.js is installed

echo.
echo 2. Checking axios dependency...
npm list axios >nul 2>&1
if errorlevel 1 (
    echo ⏳ Installing axios...
    npm install axios
    if errorlevel 1 (
        echo ❌ Failed to install axios
        pause
        exit
    )
)
echo ✅ axios is available

echo.
echo 3. Configuration Check...
findstr "YOUR_INSTANCE_ID" marketing-identity-sender.js >nul
if not errorlevel 1 (
    echo ⚠️  API not configured yet!
    echo.
    echo 📝 IMPORTANT: Configure the API first!
    echo.
    echo Steps to configure:
    echo 1. Get Green API credentials from https://green-api.com
    echo 2. Edit marketing-identity-sender.js
    echo 3. Replace YOUR_INSTANCE_ID and YOUR_API_TOKEN
    echo 4. Update your business information
    echo.
    echo Press any key to continue anyway...
    pause >nul
) else (
    echo ✅ API appears to be configured
)

echo.
echo 4. Starting Professional Marketing Sender...
echo.

node marketing-identity-sender.js

echo.
echo Program ended. Press any key to exit...
pause >nul
