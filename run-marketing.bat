@echo off
title Professional Marketing Sender
cd /d "%~dp0"

echo ========================================
echo    Professional Marketing Sender
echo ========================================
echo.
echo Send professional marketing messages
echo with your business identity!
echo.
echo Current directory: %CD%
echo.

echo Checking requirements...
echo.

echo 1. Checking Node.js...
node --version 2>nul
if errorlevel 1 (
    echo ❌ Node.js not installed!
    echo Please install Node.js from https://nodejs.org
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo ✅ Node.js is installed

echo.
echo 2. Checking if marketing file exists...
if not exist "marketing-identity-sender.js" (
    echo ❌ marketing-identity-sender.js not found!
    echo Make sure you are in the correct directory
    echo Current files:
    dir *.js
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo ✅ marketing-identity-sender.js found

echo.
echo 3. Checking axios dependency...
npm list axios >nul 2>&1
if errorlevel 1 (
    echo ⏳ Installing axios...
    npm install axios
    if errorlevel 1 (
        echo ❌ Failed to install axios
        echo Try running: npm install axios
        echo.
        echo Press any key to exit...
        pause >nul
        exit /b 1
    )
)
echo ✅ axios is available

echo.
echo 4. Configuration Check...
findstr "YOUR_INSTANCE_ID" marketing-identity-sender.js >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  API not configured yet!
    echo.
    echo 📝 IMPORTANT: Configure the API first!
    echo.
    echo Steps to configure:
    echo 1. Get Green API credentials from https://green-api.com
    echo 2. Edit marketing-identity-sender.js
    echo 3. Replace YOUR_INSTANCE_ID and YOUR_API_TOKEN
    echo 4. Update your business information
    echo.
    echo Do you want to continue anyway? (y/n)
    set /p continue="Enter choice: "
    if /i not "%continue%"=="y" (
        echo.
        echo Please configure the API first and try again.
        echo See MARKETING_SETUP.md for detailed instructions.
        echo.
        echo Press any key to exit...
        pause >nul
        exit /b 1
    )
) else (
    echo ✅ API appears to be configured
)

echo.
echo 5. Starting Professional Marketing Sender...
echo.
echo If the program closes immediately, check for errors above.
echo.

node marketing-identity-sender.js

echo.
echo Program ended.
echo If you see this message, the program ran successfully.
echo.
echo Press any key to exit...
pause >nul
