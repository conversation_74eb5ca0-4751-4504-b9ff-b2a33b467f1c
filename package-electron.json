{"name": "whatsapp-sender-pro", "version": "1.0.0", "description": "Professional WhatsApp Message Sender with QR Code and API support", "main": "electron/main.js", "homepage": "./", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "npm run build", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.whatsapp.sender.pro", "productName": "WhatsApp Sender Pro", "directories": {"output": "dist"}, "files": ["electron/**/*", "src/**/*", "numbers-groups/**/*", "package.json", "!node_modules/.cache"], "extraResources": ["numbers-groups/**/*"], "win": {"target": "nsis", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.6.0", "whatsapp-web.js": "^1.28.0", "qrcode": "^1.5.3", "electron-store": "^8.1.0"}, "keywords": ["whatsapp", "messaging", "automation", "electron", "desktop-app"], "author": "WhatsApp Sender Pro", "license": "MIT"}