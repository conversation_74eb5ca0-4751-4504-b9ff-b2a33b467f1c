# 🚀 دليل إعداد أداة التسويق الاحترافي

## 📋 **الخطوات بالتفصيل:**

### الخطوة 1: احصل على Green API (5 دقائق)

1. **اذهب إلى**: https://green-api.com
2. **اضغط "Register"** وسجل حساب مجاني
3. **اضغط "Create Instance"** لإنشاء instance جديد
4. **ستحصل على**:
   ```
   Instance ID: 1101123456
   API Token: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
   ```
5. **امسح QR Code** مرة واحدة فقط لربط WhatsApp

### الخطوة 2: اضبط معلوماتك (3 دقائق)

افتح ملف `marketing-identity-sender.js` وعدل هذا القسم:

```javascript
const MARKETING_CONFIG = {
    // ضع رقم WhatsApp الخاص بك
    realNumber: '+2***********',  // مثال: +201012345678
    
    // معلومات هويتك التسويقية
    identity: {
        businessName: 'شركة التسويق الرقمي',           // اسم شركتك
        contactPerson: 'أحمد محمد',                  // اسمك
        category: 'خدمات التسويق الإلكتروني',        // نوع الخدمة
        description: 'نساعد الشركات على النمو رقمياً', // وصف
        website: 'www.digital-marketing.com',        // موقعك
        email: '<EMAIL>'         // إيميلك
    },
    
    // ضع بيانات Green API هنا
    api: {
        instanceId: '1101123456',                    // Instance ID الخاص بك
        apiToken: 'abc123def456ghi789jkl012mno345',  // API Token الخاص بك
        url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
    }
};
```

### الخطوة 3: شغل البرنامج

```cmd
run-marketing.bat
```

أو

```cmd
node marketing-identity-sender.js
```

## 📱 **ما ستراه عند التشغيل:**

```
🚀 Professional Marketing Message Sender
📈 Send marketing messages with professional identity

📈 Professional Marketing Message Sender
🏢 Business: شركة التسويق الرقمي
👤 Contact: أحمد محمد
============================================================
1. Send introduction message
2. Send special offer message
3. Send follow-up message
4. Send bulk marketing messages
5. Configure business identity
6. Preview message templates
7. Exit
============================================================
Choose an option:
```

## 🎯 **كيفية الاستخدام:**

### 1. إرسال رسالة تعريفية:
```
اختر: 1
أدخل رقم المستقبل: ***********
أدخل رسالتك: نحن متخصصون في التسويق الرقمي ونساعد الشركات على زيادة مبيعاتها
```

**النتيجة المرسلة:**
```
Hello! I'm أحمد محمد from شركة التسويق الرقمي.
We specialize in خدمات التسويق الإلكتروني.

نحن متخصصون في التسويق الرقمي ونساعد الشركات على زيادة مبيعاتها

---
Best regards,
أحمد محمد
شركة التسويق الرقمي
📧 <EMAIL>
🌐 www.digital-marketing.com

Note: This message was sent for marketing purposes. Reply STOP to unsubscribe.
```

### 2. إرسال عرض خاص:
```
اختر: 2
أدخل رقم المستقبل: ***********
أدخل تفاصيل العرض: خصم 50% على خدمات التسويق الرقمي لأول 10 عملاء
```

### 3. إرسال رسائل جماعية:
```
اختر: 4
أدخل الأرقام: ***********,01987654321,01555666777
اختر القالب: 1 (تعريفي)
أدخل الرسالة: نحن نقدم خدمات تسويق متميزة
```

## 🔧 **استكشاف الأخطاء:**

### خطأ: "API not configured"
**الحل:**
1. تأكد من استبدال `YOUR_INSTANCE_ID` برقم instance الخاص بك
2. تأكد من استبدال `YOUR_API_TOKEN` بـ token الخاص بك

### خطأ: "Connection failed"
**الحل:**
1. تحقق من اتصال الإنترنت
2. تأكد من صحة بيانات Green API
3. تحقق من أن WhatsApp مربوط بـ Green API

### خطأ: "Message failed"
**الحل:**
1. تحقق من صحة رقم المستقبل
2. تأكد من أن المستقبل لديه WhatsApp
3. تحقق من حالة Green API

## 📋 **مثال كامل للتكوين:**

```javascript
const MARKETING_CONFIG = {
    realNumber: '+201012345678',
    
    identity: {
        businessName: 'شركة الحلول التقنية',
        contactPerson: 'محمد أحمد',
        category: 'تطوير المواقع والتطبيقات',
        description: 'نقدم حلول تقنية متكاملة للشركات',
        website: 'www.tech-solutions.com',
        email: '<EMAIL>'
    },
    
    api: {
        instanceId: '1101987654',
        apiToken: 'xyz789abc123def456ghi789jkl012mno345pqr678stu901',
        url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiToken}'
    }
};
```

## 🎉 **النتيجة النهائية:**

بعد الإعداد ستتمكن من:
- ✅ إرسال رسائل تسويقية احترافية
- ✅ استخدام هويتك التجارية
- ✅ إرسال رسائل جماعية منظمة
- ✅ بناء ثقة العملاء من خلال الشفافية
- ✅ تجنب المشاكل القانونية

**🚀 ابدأ الآن: شغل `run-marketing.bat` واتبع الخطوات!**
