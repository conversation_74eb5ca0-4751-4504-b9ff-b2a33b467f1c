@echo off
title Building WhatsApp Sender Pro EXE
chcp 65001 >nul

echo ========================================
echo    Building WhatsApp Sender Pro EXE
echo ========================================
echo.

echo 1. Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not installed!
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% is installed

echo.
echo 2. Checking project files...
if not exist "package-electron.json" (
    echo ❌ package-electron.json not found!
    echo Make sure you are in the correct directory
    pause
    exit /b 1
)
if not exist "electron\main.js" (
    echo ❌ electron\main.js not found!
    echo Make sure all project files are present
    pause
    exit /b 1
)
if not exist "src\index.html" (
    echo ❌ src\index.html not found!
    echo Make sure all project files are present
    pause
    exit /b 1
)
echo ✅ All project files found

echo.
echo 3. Preparing build environment...
copy "package-electron.json" "package.json" >nul 2>&1
if not exist "assets" mkdir "assets"
if not exist "numbers-groups" mkdir "numbers-groups"
echo ✅ Environment prepared

echo.
echo 4. Cleaning previous builds...
if exist "dist" rmdir /s /q "dist"
if exist "node_modules" (
    echo Cleaning node_modules...
    rmdir /s /q "node_modules"
)
echo ✅ Cleaned previous builds

echo.
echo 5. Installing dependencies...
echo This may take 5-10 minutes depending on your internet speed...
echo Please be patient...
npm install --no-optional --no-audit
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    echo.
    echo Trying alternative installation...
    npm install --force --no-optional
    if errorlevel 1 (
        echo ❌ Installation failed completely
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
)
echo ✅ Dependencies installed successfully

echo.
echo 6. Creating default icon...
echo Creating placeholder icon for build...
echo. > "assets\icon.png"
echo ✅ Default assets ready

echo.
echo 7. Building Windows EXE...
echo This will take 10-15 minutes...
echo Please wait and do not close this window...
echo.
npm run build-win
if errorlevel 1 (
    echo ❌ Build failed!
    echo.
    echo Common solutions:
    echo 1. Run as Administrator
    echo 2. Disable antivirus temporarily
    echo 3. Check internet connection
    echo 4. Try: npm cache clean --force
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Build Completed Successfully!
echo ========================================
echo.
echo 📁 Output location: dist/
echo 📦 Installer: dist/WhatsApp Sender Pro Setup.exe
echo 💻 Portable: dist/win-unpacked/WhatsApp Sender Pro.exe
echo.
echo 🎉 Your WhatsApp Sender Pro is ready!
echo.

echo Opening output directory...
start "" "dist"

echo.
echo Press any key to exit...
pause >nul
