@echo off
title WhatsApp Sender Pro - تشغيل مباشر
color 0A

echo.
echo ==========================================
echo      WhatsApp Sender Pro - تشغيل مباشر
echo ==========================================
echo.

echo 🚀 يتم تشغيل WhatsApp Sender Pro...
echo.

echo ✅ لا يحتاج تثبيت Python
echo ✅ لا يحتاج تثبيت Node.js
echo ✅ لا توجد مشاكل EBUSY
echo ✅ يعمل مباشرة في المتصفح
echo.

echo جاري فتح التطبيق...

REM فتح الملف في المتصفح الافتراضي
start "" "WhatsApp-Sender-Pro.html"

REM محاولة فتح في متصفحات مختلفة
timeout /t 2 >nul
start chrome "WhatsApp-Sender-Pro.html" >nul 2>&1
start firefox "WhatsApp-Sender-Pro.html" >nul 2>&1
start msedge "WhatsApp-Sender-Pro.html" >nul 2>&1

echo.
echo ✅ تم فتح WhatsApp Sender Pro!
echo.
echo المميزات المتوفرة:
echo ⚙️ إعدادات API
echo 📱 QR Code للإرسال السريع (جديد!)
echo 📱 إرسال رسائل فردية
echo 📊 إرسال رسائل جماعية
echo 👥 إدارة مجموعات الأرقام
echo 💾 حفظ البيانات محلياً
echo 📁 استيراد أرقام من ملفات
echo 🌐 إضافة موقع الويب للرسائل
echo 💾 تحميل وطباعة QR Code
echo.

echo إذا لم يفتح التطبيق:
echo 1. انقر مرتين على ملف WhatsApp-Sender-Pro.html
echo 2. اختر فتح بـ Chrome أو Firefox أو Edge
echo.

echo ==========================================
echo اضغط أي زر لإغلاق هذه النافذة
echo ==========================================
pause >nul
exit
