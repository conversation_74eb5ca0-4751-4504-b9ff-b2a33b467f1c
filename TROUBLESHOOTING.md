# 🔧 حل المشاكل الشائعة

## مشكلة: start.bat لا يفتح

### الأسباب المحتملة:
1. **Node.js غير مثبت**
2. **المكتبات غير مثبتة**
3. **مشكلة في الملفات**
4. **مشكلة في الصلاحيات**

### الحلول:

#### 1️⃣ تحقق من Node.js
```cmd
node --version
```
- إذا ظهر رقم الإصدار ✅ Node.js مثبت
- إذا ظهر خطأ ❌ قم بتثبيت Node.js من [nodejs.org](https://nodejs.org)

#### 2️⃣ تثبيت المكتبات
```cmd
npm install
```

#### 3️⃣ تشغيل التشخيص
```cmd
diagnose.bat
```

#### 4️⃣ تشغيل الاختبار البسيط
```cmd
run-simple.bat
```

## مشكلة: البرنامج يتوقف عند التشغيل

### الحلول:
1. **انتظر دقيقة كاملة** - تحميل المتصفح يحتاج وقت
2. **أعد تشغيل الكمبيوتر**
3. **احذف مجلد `.wwebjs_auth`**
4. **تشغيل كـ Administrator**

## مشكلة: QR Code لا يظهر

### الحلول:
1. **تحديث الطرفية/CMD**
2. **تشغيل في PowerShell بدلاً من CMD**
3. **تحقق من اتصال الإنترنت**

## مشكلة: فشل في إرسال الرسالة

### الحلول:
1. **تحقق من صحة رقم الهاتف**
2. **تأكد أن الرقم مسجل في WhatsApp**
3. **تحقق من اتصال الإنترنت**
4. **أعد مسح QR Code**

## طرق التشغيل البديلة:

### الطريقة 1: مباشرة من CMD
```cmd
cd "مسار المجلد"
node whatsapp-sender.js
```

### الطريقة 2: باستخدام npm
```cmd
npm start
```

### الطريقة 3: الوضع السريع
```cmd
npm run quick
```

## أوامر مفيدة للتشخيص:

```cmd
# فحص Node.js
node --version

# فحص npm
npm --version

# فحص المكتبات
npm list

# إعادة تثبيت المكتبات
npm install --force

# تنظيف الكاش
npm cache clean --force

# فحص الملفات
dir *.js

# اختبار بسيط
node test-simple.js
```

## إعدادات النظام المطلوبة:

- **Node.js**: الإصدار 14 أو أحدث
- **الذاكرة**: 2 جيجا على الأقل
- **مساحة القرص**: 500 ميجا على الأقل
- **الإنترنت**: اتصال مستقر

## رسائل الخطأ الشائعة:

### `'node' is not recognized`
**الحل**: تثبيت Node.js وإضافته لـ PATH

### `Cannot find module`
**الحل**: `npm install`

### `EACCES permission denied`
**الحل**: تشغيل كـ Administrator

### `Error: spawn ENOENT`
**الحل**: إعادة تثبيت Node.js

## الدعم:

إذا لم تحل المشاكل:
1. شغل `diagnose.bat` وأرسل النتائج
2. تحقق من ملف `QUICK_START.md`
3. جرب الأمثلة في `example-config.js`

---

**💡 نصيحة**: احتفظ بهذا الملف كمرجع لحل المشاكل!
