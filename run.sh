#!/bin/bash

# أداة إرسال رسائل WhatsApp
# للأنظمة التي تدعم Bash

clear
echo "=========================================="
echo "    أداة إرسال رسائل WhatsApp"
echo "=========================================="
echo ""
echo "اختر طريقة التشغيل:"
echo ""
echo "1. التشغيل التفاعلي (مع قائمة)"
echo "2. التشغيل السريع (إعدادات مسبقة)"
echo "3. عرض التعليمات"
echo "4. الخروج"
echo ""
read -p "أدخل اختيارك (1-4): " choice

case $choice in
    1)
        echo ""
        echo "جاري تشغيل الوضع التفاعلي..."
        node whatsapp-sender.js
        ;;
    2)
        echo ""
        echo "جاري تشغيل الوضع السريع..."
        node quick-send.js
        ;;
    3)
        echo ""
        echo "=========================================="
        echo "            التعليمات"
        echo "=========================================="
        echo ""
        echo "1. الوضع التفاعلي:"
        echo "   - يعرض قائمة تفاعلية"
        echo "   - يمكنك إرسال رسالة واحدة أو متعددة"
        echo "   - مناسب للاستخدام اليومي"
        echo ""
        echo "2. الوضع السريع:"
        echo "   - يحتاج إعداد مسبق في الملف"
        echo "   - يرسل الرسائل تلقائياً"
        echo "   - مناسب للإرسال المجدول"
        echo ""
        echo "3. متطلبات التشغيل:"
        echo "   - Node.js مثبت على النظام"
        echo "   - اتصال بالإنترنت"
        echo "   - هاتف ذكي مع WhatsApp"
        echo ""
        echo "4. خطوات الاستخدام:"
        echo "   - شغل البرنامج"
        echo "   - امسح QR Code بهاتفك"
        echo "   - أدخل الرقم والرسالة"
        echo "   - اضغط Enter للإرسال"
        echo ""
        read -p "اضغط Enter للعودة للقائمة..."
        exec "$0"
        ;;
    4)
        echo ""
        echo "شكراً لاستخدام الأداة!"
        exit 0
        ;;
    *)
        echo ""
        echo "اختيار غير صحيح!"
        sleep 2
        exec "$0"
        ;;
esac

echo ""
echo "اضغط Enter للعودة للقائمة..."
read
exec "$0"
