@echo off
title Simple WhatsApp Sender Build
chcp 65001 >nul

echo ========================================
echo    Simple WhatsApp Sender Build
echo ========================================
echo.

echo This is a simplified build process for testing.
echo.

echo 1. Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not installed!
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js is available

echo.
echo 2. Preparing package.json...
if exist "package-electron.json" (
    copy "package-electron.json" "package.json" >nul
    echo ✅ Package file ready
) else (
    echo ❌ package-electron.json not found!
    pause
    exit /b 1
)

echo.
echo 3. Creating directories...
if not exist "assets" mkdir "assets"
if not exist "numbers-groups" mkdir "numbers-groups"
echo ✅ Directories created

echo.
echo 4. Installing basic dependencies...
echo Installing electron only for testing...
npm install electron --save-dev
if errorlevel 1 (
    echo ❌ Failed to install electron
    pause
    exit /b 1
)
echo ✅ Electron installed

echo.
echo 5. Testing the application...
echo Starting WhatsApp Sender Pro...
echo Close the app window to continue...
npm start
if errorlevel 1 (
    echo ❌ App failed to start
    echo Check the console for errors
    pause
    exit /b 1
)

echo.
echo ✅ App test completed!
echo.
echo If the app worked correctly, you can now try:
echo - build-exe.bat for full build
echo - Or install more dependencies manually
echo.
pause
