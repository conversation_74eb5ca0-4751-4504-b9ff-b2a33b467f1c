@echo off
title Test WhatsApp Sender Pro
chcp 65001 >nul

echo ========================================
echo    Test WhatsApp Sender Pro
echo ========================================
echo.

echo This will test the app in browser mode first.
echo.

echo 1. Checking files...
if not exist "src\index.html" (
    echo ❌ src\index.html not found!
    pause
    exit /b 1
)
if not exist "src\styles.css" (
    echo ❌ src\styles.css not found!
    pause
    exit /b 1
)
if not exist "src\app.js" (
    echo ❌ src\app.js not found!
    pause
    exit /b 1
)
echo ✅ All UI files found

echo.
echo 2. Creating test directories...
if not exist "numbers-groups" mkdir "numbers-groups"
if not exist "assets" mkdir "assets"

echo.
echo 3. Creating sample data...
if not exist "numbers-groups\test-group.txt" (
    echo # Test group > "numbers-groups\test-group.txt"
    echo 01012345678 >> "numbers-groups\test-group.txt"
    echo 01123456789 >> "numbers-groups\test-group.txt"
    echo 01234567890 >> "numbers-groups\test-group.txt"
)

echo.
echo 4. Opening app in browser for UI testing...
echo The app will open in your default browser.
echo Some features will be in demo mode.
echo.
echo Press any key to open the app...
pause >nul

start "" "src\index.html"

echo.
echo ✅ App opened in browser!
echo.
echo Test the following:
echo - Navigation between tabs
echo - UI responsiveness
echo - Form inputs
echo - Button clicks
echo - Toast notifications
echo.
echo If the UI works well, try building the desktop version:
echo - simple-build.bat (for quick test)
echo - build-exe.bat (for full build)
echo.
pause
