const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const readline = require('readline');

class WhatsAppSender {
    constructor() {
        this.client = new Client({
            authStrategy: new LocalAuth(),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            }
        });

        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        this.setupEventHandlers();
    }

    setupEventHandlers() {
        // عرض QR Code للمسح
        this.client.on('qr', (qr) => {
            console.log('\n🔗 امسح الـ QR Code التالي بهاتفك لتسجيل الدخول:');
            qrcode.generate(qr, { small: true });
            console.log('\n📱 افتح WhatsApp على هاتفك > الإعدادات > الأجهزة المرتبطة > ربط جهاز');
        });

        // عند تسجيل الدخول بنجاح
        this.client.on('ready', () => {
            console.log('\n✅ تم تسجيل الدخول بنجاح! يمكنك الآن إرسال الرسائل.');
            this.showMenu();
        });

        // عند قطع الاتصال
        this.client.on('disconnected', (reason) => {
            console.log('\n❌ تم قطع الاتصال:', reason);
        });

        // معالجة الأخطاء
        this.client.on('auth_failure', (msg) => {
            console.error('\n❌ فشل في المصادقة:', msg);
        });
    }

    // تنسيق رقم الهاتف
    formatPhoneNumber(phoneNumber) {
        // إزالة جميع الرموز غير الرقمية
        let cleanNumber = phoneNumber.replace(/\D/g, '');
        
        // إضافة رمز الدولة إذا لم يكن موجوداً
        if (!cleanNumber.startsWith('20') && cleanNumber.length === 11) {
            cleanNumber = '20' + cleanNumber;
        } else if (!cleanNumber.startsWith('20') && cleanNumber.length === 10) {
            cleanNumber = '20' + cleanNumber;
        }
        
        return cleanNumber + '@c.us';
    }

    // التحقق من صحة الرقم
    async isValidNumber(phoneNumber) {
        try {
            const formattedNumber = this.formatPhoneNumber(phoneNumber);
            const isRegistered = await this.client.isRegisteredUser(formattedNumber);
            return isRegistered;
        } catch (error) {
            return false;
        }
    }

    // إرسال رسالة
    async sendMessage(phoneNumber, message) {
        try {
            const formattedNumber = this.formatPhoneNumber(phoneNumber);
            
            // التحقق من صحة الرقم
            const isValid = await this.isValidNumber(phoneNumber);
            if (!isValid) {
                console.log('❌ الرقم غير صحيح أو غير مسجل في WhatsApp');
                return false;
            }

            // إرسال الرسالة
            await this.client.sendMessage(formattedNumber, message);
            console.log('✅ تم إرسال الرسالة بنجاح!');
            return true;
        } catch (error) {
            console.error('❌ خطأ في إرسال الرسالة:', error.message);
            return false;
        }
    }

    // عرض القائمة الرئيسية
    showMenu() {
        console.log('\n' + '='.repeat(50));
        console.log('📱 أداة إرسال رسائل WhatsApp');
        console.log('='.repeat(50));
        console.log('1. إرسال رسالة جديدة');
        console.log('2. إرسال رسالة لعدة أرقام');
        console.log('3. الخروج');
        console.log('='.repeat(50));

        this.rl.question('اختر رقم الخيار: ', (choice) => {
            this.handleMenuChoice(choice);
        });
    }

    // معالجة اختيار المستخدم
    handleMenuChoice(choice) {
        switch (choice) {
            case '1':
                this.sendSingleMessage();
                break;
            case '2':
                this.sendMultipleMessages();
                break;
            case '3':
                this.exit();
                break;
            default:
                console.log('❌ اختيار غير صحيح، حاول مرة أخرى');
                this.showMenu();
        }
    }

    // إرسال رسالة واحدة
    sendSingleMessage() {
        console.log('\n📝 إرسال رسالة جديدة');
        console.log('-'.repeat(30));

        this.rl.question('أدخل رقم الهاتف (مثال: 01234567890): ', (phoneNumber) => {
            if (!phoneNumber.trim()) {
                console.log('❌ يجب إدخال رقم الهاتف');
                this.showMenu();
                return;
            }

            this.rl.question('أدخل الرسالة: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ يجب إدخال نص الرسالة');
                    this.showMenu();
                    return;
                }

                console.log('\n⏳ جاري إرسال الرسالة...');
                await this.sendMessage(phoneNumber, message);
                
                setTimeout(() => {
                    this.showMenu();
                }, 2000);
            });
        });
    }

    // إرسال رسائل متعددة
    sendMultipleMessages() {
        console.log('\n📝 إرسال رسالة لعدة أرقام');
        console.log('-'.repeat(30));

        this.rl.question('أدخل الأرقام مفصولة بفاصلة (مثال: 01234567890,01987654321): ', (phoneNumbers) => {
            if (!phoneNumbers.trim()) {
                console.log('❌ يجب إدخال الأرقام');
                this.showMenu();
                return;
            }

            const numbers = phoneNumbers.split(',').map(num => num.trim()).filter(num => num);
            
            this.rl.question('أدخل الرسالة: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ يجب إدخال نص الرسالة');
                    this.showMenu();
                    return;
                }

                console.log('\n⏳ جاري إرسال الرسائل...');
                
                for (let i = 0; i < numbers.length; i++) {
                    console.log(`\n📤 إرسال للرقم ${i + 1}/${numbers.length}: ${numbers[i]}`);
                    await this.sendMessage(numbers[i], message);
                    
                    // انتظار ثانيتين بين كل رسالة لتجنب الحظر
                    if (i < numbers.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                }

                console.log('\n✅ تم الانتهاء من إرسال جميع الرسائل!');
                
                setTimeout(() => {
                    this.showMenu();
                }, 3000);
            });
        });
    }

    // الخروج من البرنامج
    exit() {
        console.log('\n👋 شكراً لاستخدام أداة إرسال رسائل WhatsApp');
        this.rl.close();
        this.client.destroy();
        process.exit(0);
    }

    // بدء تشغيل البرنامج
    async start() {
        console.log('🚀 جاري بدء تشغيل أداة إرسال رسائل WhatsApp...');
        console.log('⏳ يرجى الانتظار...\n');
        
        try {
            await this.client.initialize();
        } catch (error) {
            console.error('❌ خطأ في بدء التشغيل:', error);
            process.exit(1);
        }
    }
}

// بدء تشغيل البرنامج
const whatsappSender = new WhatsAppSender();
whatsappSender.start();

// معالجة إغلاق البرنامج
process.on('SIGINT', () => {
    console.log('\n\n👋 جاري إغلاق البرنامج...');
    whatsappSender.exit();
});
