const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const readline = require('readline');

class WhatsAppSender {
    constructor() {
        this.client = new Client({
            authStrategy: new LocalAuth(),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            }
        });

        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        this.setupEventHandlers();
    }

    setupEventHandlers() {
        // Display QR Code for scanning
        this.client.on('qr', (qr) => {
            console.log('\n🔗 Scan the following QR Code with your phone to login:');
            qrcode.generate(qr, { small: true });
            console.log('\n📱 Open WhatsApp on your phone > Settings > Linked Devices > Link a Device');
        });

        // When login is successful
        this.client.on('ready', () => {
            console.log('\n✅ Login successful! You can now send messages.');
            this.showMenu();
        });

        // When disconnected
        this.client.on('disconnected', (reason) => {
            console.log('\n❌ Disconnected:', reason);
        });

        // Error handling
        this.client.on('auth_failure', (msg) => {
            console.error('\n❌ Authentication failed:', msg);
        });
    }

    // Format phone number
    formatPhoneNumber(phoneNumber) {
        // Remove all non-numeric characters
        let cleanNumber = phoneNumber.replace(/\D/g, '');

        // Add country code if not present (assuming Egypt +20)
        if (!cleanNumber.startsWith('20') && cleanNumber.length === 11) {
            cleanNumber = '20' + cleanNumber;
        } else if (!cleanNumber.startsWith('20') && cleanNumber.length === 10) {
            cleanNumber = '20' + cleanNumber;
        }

        return cleanNumber + '@c.us';
    }

    // Validate phone number
    async isValidNumber(phoneNumber) {
        try {
            const formattedNumber = this.formatPhoneNumber(phoneNumber);
            const isRegistered = await this.client.isRegisteredUser(formattedNumber);
            return isRegistered;
        } catch (error) {
            return false;
        }
    }

    // Send message
    async sendMessage(phoneNumber, message) {
        try {
            const formattedNumber = this.formatPhoneNumber(phoneNumber);

            // Validate phone number
            const isValid = await this.isValidNumber(phoneNumber);
            if (!isValid) {
                console.log('❌ Invalid number or not registered on WhatsApp');
                return false;
            }

            // Send message
            await this.client.sendMessage(formattedNumber, message);
            console.log('✅ Message sent successfully!');
            return true;
        } catch (error) {
            console.error('❌ Error sending message:', error.message);
            return false;
        }
    }

    // Show main menu
    showMenu() {
        console.log('\n' + '='.repeat(50));
        console.log('📱 WhatsApp Message Sender');
        console.log('='.repeat(50));
        console.log('1. Send single message');
        console.log('2. Send message to multiple numbers');
        console.log('3. Exit');
        console.log('='.repeat(50));

        this.rl.question('Choose an option: ', (choice) => {
            this.handleMenuChoice(choice);
        });
    }

    // Handle user choice
    handleMenuChoice(choice) {
        switch (choice) {
            case '1':
                this.sendSingleMessage();
                break;
            case '2':
                this.sendMultipleMessages();
                break;
            case '3':
                this.exit();
                break;
            default:
                console.log('❌ Invalid choice, please try again');
                this.showMenu();
        }
    }

    // Send single message
    sendSingleMessage() {
        console.log('\n📝 Send New Message');
        console.log('-'.repeat(30));

        this.rl.question('Enter phone number (example: 01234567890): ', (phoneNumber) => {
            if (!phoneNumber.trim()) {
                console.log('❌ Phone number is required');
                this.showMenu();
                return;
            }

            this.rl.question('Enter message: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Message text is required');
                    this.showMenu();
                    return;
                }

                console.log('\n⏳ Sending message...');
                await this.sendMessage(phoneNumber, message);

                setTimeout(() => {
                    this.showMenu();
                }, 2000);
            });
        });
    }

    // Send multiple messages
    sendMultipleMessages() {
        console.log('\n📝 Send Message to Multiple Numbers');
        console.log('-'.repeat(30));

        this.rl.question('Enter phone numbers separated by comma (example: 01234567890,01987654321): ', (phoneNumbers) => {
            if (!phoneNumbers.trim()) {
                console.log('❌ Phone numbers are required');
                this.showMenu();
                return;
            }

            const numbers = phoneNumbers.split(',').map(num => num.trim()).filter(num => num);

            this.rl.question('Enter message: ', async (message) => {
                if (!message.trim()) {
                    console.log('❌ Message text is required');
                    this.showMenu();
                    return;
                }

                console.log('\n⏳ Sending messages...');

                for (let i = 0; i < numbers.length; i++) {
                    console.log(`\n📤 Sending to number ${i + 1}/${numbers.length}: ${numbers[i]}`);
                    await this.sendMessage(numbers[i], message);

                    // Wait 2 seconds between messages to avoid being blocked
                    if (i < numbers.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                }

                console.log('\n✅ All messages sent successfully!');

                setTimeout(() => {
                    this.showMenu();
                }, 3000);
            });
        });
    }

    // Exit program
    exit() {
        console.log('\n👋 Thank you for using WhatsApp Message Sender!');
        this.rl.close();
        this.client.destroy();
        process.exit(0);
    }

    // Start the program
    async start() {
        console.log('🚀 Starting WhatsApp Message Sender...');
        console.log('⏳ Please wait...\n');

        try {
            await this.client.initialize();
        } catch (error) {
            console.error('❌ Startup error:', error);
            process.exit(1);
        }
    }
}

// Start the program
const whatsappSender = new WhatsAppSender();
whatsappSender.start();

// Handle program termination
process.on('SIGINT', () => {
    console.log('\n\n👋 Closing program...');
    whatsappSender.exit();
});
