# 📱 إرسال من رقمك الخاص بدون QR Code

## ✅ الحل الذي طلبته متوفر!

يمكنك الآن إرسال رسائل WhatsApp من رقمك الخاص بدون الحاجة لمسح QR Code في كل مرة!

## 🎯 ما ستحصل عليه:

- ✅ **إرسال من رقمك الخاص** - الرسائل تظهر من رقم WhatsApp الخاص بك
- ✅ **بدون QR Code متكرر** - تحتاج مسح واحد فقط في البداية
- ✅ **إرسال تلقائي** - يمكن برمجة الإرسال
- ✅ **إرسال جماعي** - لعدة أرقام في نفس الوقت
- ✅ **واجهة سهلة** - قوائم بسيطة باللغة الإنجليزية

## 🚀 طريقة الإعداد (5 دقائق):

### الخطوة 1: احصل على API مجاني
1. اذهب إلى **https://green-api.com**
2. سجل حساب مجاني
3. أنشئ instance جديد
4. ستحصل على:
   - **Instance ID** (مثل: `1101123456`)
   - **API Token** (مثل: `abc123def456...`)

### الخطوة 2: اربط رقم WhatsApp الخاص بك
1. في لوحة Green API، ستجد QR Code
2. افتح WhatsApp على هاتفك
3. اذهب إلى **الإعدادات > الأجهزة المرتبطة > ربط جهاز**
4. امسح QR Code **مرة واحدة فقط**
5. رقمك أصبح مربوط بالـ API

### الخطوة 3: اضبط الإعدادات
1. افتح ملف `simple-api-sender.js`
2. ابحث عن قسم `CONFIG` في أعلى الملف
3. استبدل هذه القيم:
```javascript
const CONFIG = {
    instanceId: '1101123456',           // ضع Instance ID الخاص بك
    apiToken: 'abc123def456...',        // ضع API Token الخاص بك
    senderNumber: '+201234567890',      // ضع رقم WhatsApp الخاص بك
};
```

### الخطوة 4: شغل البرنامج
```bash
run-api.bat
```
أو
```bash
node simple-api-sender.js
```

## 📱 كيف تستخدمه:

### إرسال رسالة واحدة:
```
اختر الخيار: 1
أدخل رقم المستقبل: 01234567890
أدخل الرسالة: مرحباً! هذه رسالة من رقمي الخاص
✅ تم الإرسال بنجاح!
```

### إرسال رسائل جماعية:
```
اختر الخيار: 2
أدخل الأرقام: 01234567890,01987654321,01555666777
أدخل الرسالة: إعلان مهم للجميع!
✅ تم إرسال 3/3 رسائل بنجاح!
```

## 🎯 الملفات المطلوبة:

1. **`simple-api-sender.js`** - البرنامج الرئيسي (سهل الاستخدام)
2. **`run-api.bat`** - ملف التشغيل للويندوز
3. **`API_SETUP_GUIDE.md`** - دليل الإعداد المفصل
4. **`quick-config-example.js`** - أمثلة للتكوين

## 💰 التكلفة:

- **Green API**: 1000 رسالة مجاناً شهرياً
- **بعد ذلك**: حوالي $10 شهرياً للاستخدام المتوسط
- **مقارنة**: أرخص بكثير من الحلول التجارية الأخرى

## 🔒 الأمان:

- ✅ **آمن تماماً** - تستخدم API رسمي
- ✅ **رقمك محمي** - لا يمكن لأحد الوصول لرسائلك الشخصية
- ✅ **تحكم كامل** - أنت تتحكم في ما يتم إرساله
- ✅ **لا توجد برامج ضارة** - كل شيء شرعي ومعتمد

## ⚠️ ملاحظات مهمة:

1. **رقمك فقط**: يمكنك الإرسال من رقمك الخاص فقط
2. **مسح واحد**: تحتاج مسح QR Code مرة واحدة فقط في البداية
3. **احترم القوانين**: لا ترسل رسائل مزعجة
4. **موافقة المستقبلين**: تأكد من موافقة الناس على استقبال رسائلك

## 🆘 إذا واجهت مشاكل:

### "API not configured":
- تأكد من استبدال `YOUR_INSTANCE_ID` و `YOUR_API_TOKEN`
- راجع ملف `quick-config-example.js`

### "Connection failed":
- تحقق من اتصال الإنترنت
- تأكد من صحة بيانات API
- تحقق من أن WhatsApp ما زال مربوط

### "Message failed":
- تحقق من صحة رقم المستقبل
- تأكد من أن المستقبل لديه WhatsApp
- تحقق من حالة خدمة API

## 🎉 النتيجة النهائية:

بعد الإعداد، ستتمكن من:
- ✅ إرسال رسائل من رقمك الخاص
- ✅ بدون مسح QR Code في كل مرة
- ✅ إرسال تلقائي ومبرمج
- ✅ إرسال جماعي لعدة أرقام
- ✅ واجهة سهلة الاستخدام

**🚀 ابدأ الآن: شغل `run-api.bat` واتبع التعليمات!**
