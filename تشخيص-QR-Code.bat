@echo off
title تشخيص QR Code - WhatsApp Sender Pro
color 0E

echo.
echo ==========================================
echo      تشخيص QR Code - WhatsApp Sender Pro
echo ==========================================
echo.

echo 🔍 سيتم فتح التطبيق مع تشخيص شامل لـ QR Code
echo.

echo خطوات التشخيص:
echo 1️⃣ فتح التطبيق
echo 2️⃣ فتح Developer Tools (F12)
echo 3️⃣ مراقبة رسائل Console
echo 4️⃣ اختبار إنشاء QR Code
echo 5️⃣ تحليل النتائج
echo.

echo جاري فتح التطبيق...
start "" "WhatsApp-Sender-Pro.html"

echo.
echo ✅ تم فتح التطبيق!
echo.

echo 📋 خطوات التشخيص التفصيلية:
echo.

echo [الخطوة 1] فتح Developer Tools:
echo • اضغط F12 في المتصفح
echo • اذهب لتبويب Console
echo • ابحث عن الرسائل التالية:
echo   ✅ "مكتبة QR Code محملة بنجاح"
echo   ✅ "مكتبة QR Code جاهزة"
echo   ❌ "مكتبة QR Code غير محملة"
echo.

echo [الخطوة 2] اختبار ربط الهاتف:
echo • اذهب لتبويب "إعدادات API"
echo • اختر "هاتف المرسل"
echo • اضغط "ربط الهاتف"
echo • راقب الرسائل في Console:
echo   ✅ "بدء إنشاء QR Code للربط"
echo   ✅ "إنشاء QR Code بالبيانات"
echo   ✅ "تم إنشاء QR Code بنجاح"
echo.

echo [الخطوة 3] تحليل المشاكل المحتملة:
echo.

echo ❌ إذا ظهر "مكتبة QR Code غير محملة":
echo   الحل: تحقق من اتصال الإنترنت
echo   الحل: جرب متصفح مختلف
echo   الحل: أعد تحميل الصفحة
echo.

echo ❌ إذا ظهر "عنصر Canvas غير موجود":
echo   الحل: أعد تحميل الصفحة (F5)
echo   الحل: تأكد من فتح التبويب الصحيح
echo   الحل: امسح cache المتصفح
echo.

echo ❌ إذا ظهر "فشل إنشاء QR Code":
echo   الحل: اضغط "إعادة إنشاء QR Code"
echo   الحل: تحقق من Console للتفاصيل
echo   الحل: جرب بيانات مختلفة
echo.

echo ❌ إذا لم يظهر QR Code نهائياً:
echo   الحل: تحقق من رسائل Console
echo   الحل: تأكد من تحميل مكتبة QR Code
echo   الحل: جرب إعادة تحميل الصفحة
echo   الحل: استخدم متصفح مختلف
echo.

echo [الخطوة 4] اختبار متقدم:
echo • افتح Console (F12)
echo • اكتب: typeof QRCode
echo • النتيجة المتوقعة: "object" أو "function"
echo • إذا كانت "undefined" فالمكتبة غير محملة
echo.

echo [الخطوة 5] اختبار يدوي:
echo • افتح Console (F12)
echo • اكتب: testQRCodeLibrary()
echo • راقب الرسائل الظاهرة
echo.

echo 🔧 حلول سريعة:
echo.

echo للمشاكل العامة:
echo • Ctrl+F5 (إعادة تحميل قوية)
echo • امسح cache المتصفح
echo • جرب وضع التصفح الخاص
echo • استخدم متصفح مختلف
echo • تحقق من اتصال الإنترنت
echo.

echo للمشاكل المتقدمة:
echo • تعطيل إضافات المتصفح
echo • تحقق من إعدادات الأمان
echo • جرب تشغيل المتصفح كمدير
echo • تحديث المتصفح لآخر إصدار
echo.

echo 📊 النتائج المتوقعة:
echo.

echo عند النجاح ستجد:
echo ✅ QR Code يظهر خلال 3 ثوانٍ
echo ✅ رسالة "تم إنشاء QR Code بنجاح"
echo ✅ ظهور التعليمات أسفل الكود
echo ✅ ظهور زر "إعادة إنشاء QR Code"
echo ✅ تأثير بصري أخضر حول الكود
echo.

echo عند الفشل ستجد:
echo ❌ رسالة خطأ في Console
echo ❌ عدم ظهور QR Code
echo ❌ ظهور رسالة خطأ في التطبيق
echo ❌ عدم تغيير حالة الاتصال
echo.

echo 📞 للدعم الإضافي:
echo • احفظ رسائل Console
echo • اذكر نوع المتصفح والإصدار
echo • اذكر نظام التشغيل
echo • اذكر خطوات إعادة إنتاج المشكلة
echo.

echo ==========================================
echo اضغط أي زر لإغلاق هذه النافذة
echo ==========================================
pause >nul
exit
