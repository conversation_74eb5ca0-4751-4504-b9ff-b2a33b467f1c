@echo off
title Marketing Tool Test
cd /d "%~dp0"

echo ========================================
echo    Marketing Tool Diagnostic Test
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo 1. Checking Node.js installation...
node --version
if errorlevel 1 (
    echo ❌ Node.js is NOT installed
    echo Download from: https://nodejs.org
    goto :end
) else (
    echo ✅ Node.js is installed
)

echo.
echo 2. Listing JavaScript files in current directory...
dir *.js
echo.

echo 3. Checking if marketing-identity-sender.js exists...
if exist "marketing-identity-sender.js" (
    echo ✅ marketing-identity-sender.js found
) else (
    echo ❌ marketing-identity-sender.js NOT found
    echo Make sure you are in the correct directory
    goto :end
)

echo.
echo 4. Checking package.json...
if exist "package.json" (
    echo ✅ package.json found
) else (
    echo ⚠️  package.json not found
    echo This might cause dependency issues
)

echo.
echo 5. Testing Node.js syntax check...
echo Checking marketing-identity-sender.js for syntax errors...
node -c marketing-identity-sender.js
if errorlevel 1 (
    echo ❌ Syntax errors found in marketing-identity-sender.js
    goto :end
) else (
    echo ✅ No syntax errors found
)

echo.
echo 6. Checking axios dependency...
node -e "try { require('axios'); console.log('✅ axios is available'); } catch(e) { console.log('❌ axios not found:', e.message); }"

echo.
echo 7. Checking configuration...
findstr "YOUR_INSTANCE_ID" marketing-identity-sender.js >nul
if not errorlevel 1 (
    echo ⚠️  Configuration needed: YOUR_INSTANCE_ID found
    echo You need to configure Green API credentials
) else (
    echo ✅ Configuration appears to be set
)

echo.
echo 8. Testing basic Node.js execution...
echo Testing if Node.js can run JavaScript files...
node -e "console.log('✅ Node.js execution test successful')"

echo.
echo ========================================
echo    Test Complete
echo ========================================
echo.

echo If all tests show ✅, you can run the marketing tool.
echo If any test shows ❌, fix the issue first.
echo.

:end
echo Press any key to exit...
pause >nul
