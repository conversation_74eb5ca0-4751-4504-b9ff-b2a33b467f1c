// WhatsApp API Configuration File
// Choose and configure your preferred API service

module.exports = {
    // =================================================================
    // OPTION 1: Green API (Recommended - Easy to setup)
    // =================================================================
    greenapi: {
        instanceId: 'YOUR_INSTANCE_ID',     // Get from green-api.com
        apiToken: 'YOUR_API_TOKEN',         // Get from green-api.com
        url: 'https://api.green-api.com/waInstance{instanceId}/sendMessage/{apiTokenInstance}',
        
        // How to get credentials:
        // 1. Go to https://green-api.com
        // 2. Register and create instance
        // 3. Get instanceId and apiToken
        // 4. Replace values above
        
        setup_instructions: [
            '1. Visit https://green-api.com',
            '2. Register for free account',
            '3. Create new instance',
            '4. Get instanceId and apiToken',
            '5. Scan QR code ONCE to link your number',
            '6. Replace credentials above'
        ]
    },

    // =================================================================
    // OPTION 2: ChatAPI (Alternative)
    // =================================================================
    chatapi: {
        instanceId: 'YOUR_INSTANCE_ID',     // Get from chat-api.com
        token: 'YOUR_TOKEN',                // Get from chat-api.com
        url: 'https://api.chat-api.com/instance{instanceId}/sendMessage',
        
        setup_instructions: [
            '1. Visit https://chat-api.com',
            '2. Register and create instance',
            '3. Get instanceId and token',
            '4. Scan QR code ONCE to link your number',
            '5. Replace credentials above'
        ]
    },

    // =================================================================
    // OPTION 3: WhatsApp Business API (Official - More Complex)
    // =================================================================
    whatsapp_business: {
        phoneNumberId: 'YOUR_PHONE_NUMBER_ID',  // From Facebook Developer Console
        accessToken: 'YOUR_ACCESS_TOKEN',       // From Facebook Developer Console
        url: 'https://graph.facebook.com/v17.0/{phoneNumberId}/messages',
        
        setup_instructions: [
            '1. Visit https://developers.facebook.com',
            '2. Create Facebook App',
            '3. Add WhatsApp Business API',
            '4. Get phone number ID and access token',
            '5. Verify your business',
            '6. Replace credentials above'
        ]
    },

    // =================================================================
    // OPTION 4: Twilio WhatsApp API
    // =================================================================
    twilio: {
        accountSid: 'YOUR_ACCOUNT_SID',         // From Twilio Console
        authToken: 'YOUR_AUTH_TOKEN',           // From Twilio Console
        fromNumber: 'whatsapp:+***********',    // Twilio WhatsApp number
        url: 'https://api.twilio.com/2010-04-01/Accounts/{accountSid}/Messages.json',
        
        setup_instructions: [
            '1. Visit https://twilio.com',
            '2. Register and verify account',
            '3. Get Account SID and Auth Token',
            '4. Enable WhatsApp API',
            '5. Replace credentials above'
        ]
    },

    // =================================================================
    // CURRENT CONFIGURATION
    // =================================================================
    current: {
        // Change this to your preferred API
        service: 'greenapi',  // Options: 'greenapi', 'chatapi', 'whatsapp_business', 'twilio'
        
        // Your WhatsApp number (will be set by user)
        senderNumber: '',
        
        // Default settings
        retryAttempts: 3,
        delayBetweenMessages: 3000, // 3 seconds
        timeout: 30000 // 30 seconds
    }
};

// =================================================================
// QUICK SETUP GUIDE
// =================================================================

/*
EASIEST OPTION - Green API:

1. Go to https://green-api.com
2. Register (free account available)
3. Create new instance
4. You'll get:
   - instanceId (like: **********)
   - apiToken (like: 1234567890abcdef...)
5. Scan QR code ONCE with your WhatsApp
6. Replace the values above
7. Run the program!

Example configuration:
greenapi: {
    instanceId: '**********',
    apiToken: '1234567890abcdef1234567890abcdef12345678',
    // ... rest stays the same
}

Then your number will be able to send messages without scanning QR every time!
*/
