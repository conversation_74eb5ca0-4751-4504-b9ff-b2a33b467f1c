@echo off
title WhatsApp Sender Pro - Electron Diagnostics
chcp 65001 >nul

echo ========================================
echo    Electron Project Diagnostics
echo ========================================
echo.

echo 1. Node.js Check:
echo ----------------------------------------
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js NOT installed
    echo 🔧 Install from: https://nodejs.org
    goto :end
) else (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js: %%i
    for /f "tokens=*" %%i in ('npm --version') do echo ✅ NPM: %%i
)

echo.
echo 2. Project Structure Check:
echo ----------------------------------------
if exist "package-electron.json" (
    echo ✅ package-electron.json
) else (
    echo ❌ package-electron.json MISSING
    echo 🔧 This file is required for building
)

if exist "electron\main.js" (
    echo ✅ electron\main.js
) else (
    echo ❌ electron\main.js MISSING
    echo 🔧 Core Electron file missing
)

if exist "src\index.html" (
    echo ✅ src\index.html
) else (
    echo ❌ src\index.html MISSING
    echo 🔧 UI file missing
)

if exist "src\styles.css" (
    echo ✅ src\styles.css
) else (
    echo ❌ src\styles.css MISSING
    echo 🔧 Styles file missing
)

if exist "src\app.js" (
    echo ✅ src\app.js
) else (
    echo ❌ src\app.js MISSING
    echo 🔧 App logic file missing
)

echo.
echo 3. Dependencies Check:
echo ----------------------------------------
if exist "package.json" (
    echo ✅ package.json exists
) else (
    echo ⚠️  package.json missing
    echo 🔧 Will be created from package-electron.json
)

if exist "node_modules" (
    echo ✅ node_modules exists
    if exist "node_modules\electron" (
        echo ✅ Electron installed
    ) else (
        echo ❌ Electron NOT installed
        echo 🔧 Run: npm install electron
    )
) else (
    echo ❌ node_modules missing
    echo 🔧 Run: npm install
)

echo.
echo 4. Build Environment:
echo ----------------------------------------
if exist "build-exe.bat" (
    echo ✅ build-exe.bat
) else (
    echo ❌ build-exe.bat MISSING
)

if exist "simple-build.bat" (
    echo ✅ simple-build.bat
) else (
    echo ❌ simple-build.bat MISSING
)

echo.
echo 5. Previous Builds:
echo ----------------------------------------
if exist "dist" (
    echo ⚠️  Previous build found
    echo 🔧 Consider deleting dist/ folder
) else (
    echo ✅ Clean build environment
)

echo.
echo 6. Quick Tests:
echo ----------------------------------------
echo Testing Node.js execution...
node -e "console.log('✅ Node.js works')" 2>nul
if errorlevel 1 (
    echo ❌ Node.js execution failed
) else (
    echo ✅ Node.js execution successful
)

echo.
echo ========================================
echo    Recommendations
echo ========================================
echo.

echo Based on the diagnostics above:
echo.

if not exist "node_modules" (
    echo 🔧 FIRST: Run simple-build.bat
    echo    This will install basic dependencies
    echo.
)

if not exist "electron\main.js" (
    echo 🔧 CRITICAL: Missing core files
    echo    Re-download the complete project
    echo.
)

node --version >nul 2>&1
if errorlevel 1 (
    echo 🔧 CRITICAL: Install Node.js first
    echo    Download from: https://nodejs.org
    echo.
)

echo 📋 Build Process Order:
echo 1. diagnose-electron.bat  ← You are here
echo 2. simple-build.bat       ← Install dependencies
echo 3. test-app.bat          ← Test in browser
echo 4. build-exe.bat         ← Build final EXE
echo.

:end
echo Press any key to exit...
pause >nul
