@echo off
chcp 65001 >nul
title تشخيص مشاكل WhatsApp Sender

echo ========================================
echo    تشخيص مشاكل أداة WhatsApp
echo ========================================
echo.

echo 🔍 فحص النظام...
echo.

echo 1. فحص Node.js:
node --version 2>nul
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 💡 الحل: قم بتحميل وتثبيت Node.js من https://nodejs.org
    echo.
    goto :end
) else (
    echo ✅ Node.js مثبت
)

echo.
echo 2. فحص npm:
npm --version 2>nul
if errorlevel 1 (
    echo ❌ npm غير متوفر
    echo 💡 الحل: أعد تثبيت Node.js
    echo.
    goto :end
) else (
    echo ✅ npm متوفر
)

echo.
echo 3. فحص مجلد node_modules:
if exist "node_modules" (
    echo ✅ مجلد node_modules موجود
) else (
    echo ❌ مجلد node_modules غير موجود
    echo 💡 الحل: تشغيل npm install
    echo.
    set /p install="هل تريد تثبيت المكتبات الآن؟ (y/n): "
    if /i "!install!"=="y" (
        echo جاري تثبيت المكتبات...
        npm install
    )
    goto :end
)

echo.
echo 4. فحص الملفات الأساسية:
if exist "whatsapp-sender.js" (
    echo ✅ whatsapp-sender.js موجود
) else (
    echo ❌ whatsapp-sender.js غير موجود
)

if exist "quick-send.js" (
    echo ✅ quick-send.js موجود
) else (
    echo ❌ quick-send.js غير موجود
)

if exist "package.json" (
    echo ✅ package.json موجود
) else (
    echo ❌ package.json غير موجود
)

echo.
echo 5. اختبار المكتبات:
node test-simple.js 2>nul
if errorlevel 1 (
    echo ❌ خطأ في تحميل المكتبات
    echo 💡 الحل: تشغيل npm install --force
) else (
    echo ✅ المكتبات تعمل بشكل صحيح
)

echo.
echo 6. فحص الذاكرة والمعالج:
echo معلومات النظام:
systeminfo | findstr /C:"Total Physical Memory"
systeminfo | findstr /C:"Processor"

:end
echo.
echo ========================================
echo    انتهى التشخيص
echo ========================================
echo.
echo إذا كانت جميع الفحوصات ✅ يمكنك تشغيل البرنامج
echo إذا كان هناك ❌ اتبع الحلول المقترحة
echo.
pause
