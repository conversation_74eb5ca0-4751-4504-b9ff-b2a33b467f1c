<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sender Pro - Enhanced</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #075E54 0%, #128C7E 50%, #25D366 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #075E54, #128C7E);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 600px;
        }

        .sidebar {
            background: #f8f9fa;
            border-left: 1px solid #ddd;
            padding: 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            border-bottom: 1px solid #e9ecef;
        }

        .sidebar-menu a {
            display: block;
            padding: 20px 25px;
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
        }

        .sidebar-menu a:hover {
            background: #e9ecef;
            border-right-color: #25D366;
        }

        .sidebar-menu a.active {
            background: #25D366;
            color: white;
            border-right-color: #075E54;
        }

        .content-area {
            padding: 30px;
            overflow-y: auto;
            max-height: 80vh;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        .card h3 {
            color: #075E54;
            margin-bottom: 20px;
            font-size: 1.4rem;
            border-bottom: 2px solid #25D366;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #25D366;
            box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
            font-weight: 500;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .qr-container {
            text-align: center;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }

        .qr-container.active {
            border-color: #25D366;
            background: rgba(37, 211, 102, 0.05);
        }

        .progress {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin: 15px 0;
            display: none;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #25D366, #128C7E);
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload:hover {
            border-color: #25D366;
            background: rgba(37, 211, 102, 0.05);
        }

        .file-upload.dragover {
            border-color: #25D366;
            background: rgba(37, 211, 102, 0.1);
        }

        .number-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }

        .number-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .number-item:last-child {
            border-bottom: none;
        }

        .number-item:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-sending {
            background: #cce5ff;
            color: #004085;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                order: 2;
            }
            
            .content-area {
                order: 1;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }

        /* QR Code Styles */
        #qrCanvas {
            border: 3px solid #25D366;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
        }

        /* File attachment styles */
        .file-attachment {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 5px 0;
        }

        .file-attachment .file-icon {
            font-size: 1.5rem;
        }

        .file-attachment .file-info {
            flex: 1;
        }

        .file-attachment .file-name {
            font-weight: bold;
            color: #333;
        }

        .file-attachment .file-size {
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WhatsApp Sender Pro</h1>
            <p>أداة احترافية شاملة لإرسال رسائل WhatsApp مع الملفات</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <ul class="sidebar-menu">
                    <li><a href="#" class="menu-item active" data-section="dashboard">📊 لوحة التحكم</a></li>
                    <li><a href="#" class="menu-item" data-section="connection">🔗 طرق الاتصال</a></li>
                    <li><a href="#" class="menu-item" data-section="single">📱 رسالة واحدة</a></li>
                    <li><a href="#" class="menu-item" data-section="bulk">📤 إرسال جماعي</a></li>
                    <li><a href="#" class="menu-item" data-section="files">📎 إرسال ملفات</a></li>
                    <li><a href="#" class="menu-item" data-section="groups">👥 إدارة المجموعات</a></li>
                    <li><a href="#" class="menu-item" data-section="qr">📱 QR Code</a></li>
                    <li><a href="#" class="menu-item" data-section="settings">⚙️ الإعدادات</a></li>
                </ul>
            </div>

            <div class="content-area">
                <!-- Dashboard Section -->
                <div id="dashboard" class="section active">
                    <div class="card">
                        <h3>📊 لوحة التحكم الرئيسية</h3>
                        <div class="grid">
                            <div class="card">
                                <h4>📈 إحصائيات اليوم</h4>
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-top: 15px;">
                                    <div style="text-align: center; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                                        <div style="font-size: 2rem; color: #28a745;">0</div>
                                        <div style="color: #666;">رسائل مرسلة</div>
                                    </div>
                                    <div style="text-align: center; padding: 15px; background: #fff3cd; border-radius: 8px;">
                                        <div style="font-size: 2rem; color: #856404;">0</div>
                                        <div style="color: #666;">في الانتظار</div>
                                    </div>
                                </div>
                            </div>
                            <div class="card">
                                <h4>🔗 حالة الاتصال</h4>
                                <div id="connectionStatus" style="padding: 20px; text-align: center; background: #f8d7da; border-radius: 8px; color: #721c24;">
                                    ❌ غير متصل
                                </div>
                                <button class="btn" onclick="checkConnection()" style="width: 100%; margin-top: 15px;">🔍 فحص الاتصال</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Connection Section -->
                <div id="connection" class="section">
                    <div class="card">
                        <h3>🔗 اختيار طريقة الاتصال</h3>
                        <div class="grid">
                            <div class="card">
                                <h4>🤖 Green API</h4>
                                <div class="form-group">
                                    <label>Instance ID:</label>
                                    <input type="text" id="instanceId" placeholder="أدخل Instance ID">
                                </div>
                                <div class="form-group">
                                    <label>API Token:</label>
                                    <input type="password" id="apiToken" placeholder="أدخل API Token">
                                </div>
                                <button class="btn" onclick="saveAPISettings()">💾 حفظ إعدادات API</button>
                                <button class="btn btn-secondary" onclick="testAPIConnection()">🔍 اختبار الاتصال</button>
                            </div>
                            
                            <div class="card">
                                <h4>📱 WhatsApp Web</h4>
                                <div id="webConnectionStatus" style="padding: 20px; text-align: center; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
                                    📱 جاهز للربط
                                </div>
                                <button class="btn" onclick="connectWhatsAppWeb()">🌐 ربط WhatsApp Web</button>
                                <button class="btn btn-danger" onclick="disconnectWhatsAppWeb()" style="display: none;" id="disconnectWebBtn">🔌 قطع الاتصال</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Single Message Section -->
                <div id="single" class="section">
                    <div class="card">
                        <h3>📱 إرسال رسالة واحدة</h3>
                        <div class="grid">
                            <div class="card">
                                <h4>📝 بيانات الرسالة</h4>
                                <div class="form-group">
                                    <label>رقم الهاتف (مع كود الدولة):</label>
                                    <input type="text" id="singlePhone" placeholder="مثال: 201234567890">
                                </div>
                                <div class="form-group">
                                    <label>الرسالة:</label>
                                    <textarea id="singleMessage" rows="5" placeholder="اكتب رسالتك هنا..."></textarea>
                                </div>
                                <div class="form-group">
                                    <label>إرفاق ملف (اختياري):</label>
                                    <input type="file" id="singleFile" accept="image/*,video/*,audio/*,.pdf,.doc,.docx">
                                </div>
                                <button class="btn" onclick="sendSingleMessage()">📤 إرسال الرسالة</button>
                            </div>

                            <div class="card">
                                <h4>📋 قوالب الرسائل</h4>
                                <select id="messageTemplates" onchange="loadTemplate()">
                                    <option value="">اختر قالب...</option>
                                    <option value="welcome">رسالة ترحيب</option>
                                    <option value="offer">عرض خاص</option>
                                    <option value="reminder">تذكير</option>
                                    <option value="thanks">شكر</option>
                                </select>
                                <div style="margin-top: 15px;">
                                    <button class="btn btn-secondary" onclick="previewMessage()">👁️ معاينة</button>
                                    <button class="btn btn-secondary" onclick="clearSingleForm()">🗑️ مسح</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Messages Section -->
                <div id="bulk" class="section">
                    <div class="card">
                        <h3>📤 الإرسال الجماعي الذكي</h3>
                        <div class="grid">
                            <div class="card">
                                <h4>📋 قائمة الأرقام</h4>
                                <div class="form-group">
                                    <label>أدخل الأرقام (رقم في كل سطر):</label>
                                    <textarea id="bulkNumbers" rows="8" placeholder="201234567890&#10;201987654321&#10;201555666777"></textarea>
                                </div>
                                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                    <button class="btn btn-secondary" onclick="loadNumbersFromFile()">📁 تحميل من ملف</button>
                                    <button class="btn btn-secondary" onclick="loadFromGroup()">👥 من مجموعة</button>
                                    <button class="btn btn-secondary" onclick="validateNumbers()">✅ فحص الأرقام</button>
                                </div>
                            </div>

                            <div class="card">
                                <h4>💬 الرسالة والإعدادات</h4>
                                <div class="form-group">
                                    <label>الرسالة:</label>
                                    <textarea id="bulkMessage" rows="4" placeholder="اكتب رسالتك الجماعية هنا..."></textarea>
                                </div>
                                <div class="form-group">
                                    <label>التأخير بين الرسائل (ثواني):</label>
                                    <input type="number" id="delaySeconds" value="3" min="1" max="60">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="randomizeOrder"> ترتيب عشوائي للأرقام
                                    </label>
                                </div>
                                <button class="btn" onclick="startBulkSending()">🚀 بدء الإرسال الجماعي</button>
                            </div>
                        </div>

                        <!-- Progress Section -->
                        <div class="card" id="bulkProgressCard" style="display: none;">
                            <h4>📊 تقدم الإرسال</h4>
                            <div class="progress">
                                <div class="progress-bar" id="bulkProgressBar">0%</div>
                            </div>
                            <div id="bulkStatus" style="margin: 15px 0; font-weight: bold; text-align: center;"></div>
                            <div style="text-align: center;">
                                <button class="btn btn-secondary" onclick="pauseBulkSending()">⏸️ إيقاف مؤقت</button>
                                <button class="btn btn-secondary" onclick="resumeBulkSending()">▶️ متابعة</button>
                                <button class="btn btn-danger" onclick="stopBulkSending()">⏹️ إيقاف</button>
                            </div>
                        </div>

                        <!-- Results Section -->
                        <div class="card" id="bulkResultsCard" style="display: none;">
                            <h4>📈 نتائج الإرسال</h4>
                            <div class="number-list" id="bulkResults"></div>
                        </div>
                    </div>
                </div>

                <!-- Files Section -->
                <div id="files" class="section">
                    <div class="card">
                        <h3>📎 إرسال الملفات الذكي</h3>
                        <div class="grid">
                            <div class="card">
                                <h4>📁 رفع الملفات</h4>
                                <div class="file-upload" id="fileUploadArea" onclick="document.getElementById('multipleFiles').click()">
                                    <div style="font-size: 3rem; margin-bottom: 15px;">📁</div>
                                    <p>اضغط هنا أو اسحب الملفات لرفعها</p>
                                    <p style="font-size: 0.9rem; color: #666;">يدعم: الصور، الفيديو، الصوت، PDF، Word</p>
                                </div>
                                <input type="file" id="multipleFiles" multiple accept="image/*,video/*,audio/*,.pdf,.doc,.docx" style="display: none;">

                                <div id="uploadedFiles" style="margin-top: 20px;"></div>
                            </div>

                            <div class="card">
                                <h4>🎯 توزيع الملفات</h4>
                                <div class="form-group">
                                    <label>طريقة التوزيع:</label>
                                    <select id="distributionMethod">
                                        <option value="same">نفس الملف لجميع الأرقام</option>
                                        <option value="sequential">ملف مختلف لكل رقم بالترتيب</option>
                                        <option value="random">ملف عشوائي لكل رقم</option>
                                        <option value="manual">تحديد يدوي لكل رقم</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>أرقام الهواتف:</label>
                                    <textarea id="fileNumbers" rows="6" placeholder="201234567890&#10;201987654321"></textarea>
                                </div>

                                <div class="form-group">
                                    <label>رسالة مرفقة (اختياري):</label>
                                    <textarea id="fileMessage" rows="3" placeholder="رسالة مع الملف..."></textarea>
                                </div>

                                <button class="btn" onclick="startFileSending()">📤 إرسال الملفات</button>
                            </div>
                        </div>

                        <!-- Manual Assignment Section -->
                        <div class="card" id="manualAssignmentCard" style="display: none;">
                            <h4>🎯 تحديد الملفات يدوياً</h4>
                            <div id="manualAssignmentList"></div>
                            <button class="btn" onclick="applyManualAssignment()">✅ تطبيق التحديد</button>
                        </div>
                    </div>
                </div>

                <!-- QR Code Section -->
                <div id="qr" class="section">
                    <div class="card">
                        <h3>📱 مولد QR Code المتقدم</h3>
                        <div class="grid">
                            <div class="card">
                                <h4>📝 بيانات QR Code</h4>
                                <div class="form-group">
                                    <label>رقم الهاتف:</label>
                                    <input type="text" id="qrPhone" placeholder="201234567890">
                                </div>
                                <div class="form-group">
                                    <label>الرسالة:</label>
                                    <textarea id="qrMessage" rows="4" placeholder="اكتب رسالتك هنا..."></textarea>
                                </div>
                                <div class="form-group">
                                    <label>حجم QR Code:</label>
                                    <select id="qrSize">
                                        <option value="200">صغير (200x200)</option>
                                        <option value="300" selected>متوسط (300x300)</option>
                                        <option value="400">كبير (400x400)</option>
                                        <option value="500">كبير جداً (500x500)</option>
                                    </select>
                                </div>
                                <button class="btn" onclick="generateQRCode()">🔄 إنشاء QR Code</button>
                            </div>

                            <div class="card">
                                <h4>📱 QR Code المُنشأ</h4>
                                <div class="qr-container" id="qrContainer">
                                    <div id="qrPlaceholder">
                                        <div style="font-size: 3rem; margin-bottom: 15px;">📱</div>
                                        <p>أدخل البيانات واضغط "إنشاء QR Code"</p>
                                    </div>
                                    <canvas id="qrCanvas" style="display: none;"></canvas>
                                </div>

                                <div style="text-align: center; margin-top: 15px;" id="qrActions" style="display: none;">
                                    <button class="btn btn-secondary" onclick="downloadQR()">💾 تحميل</button>
                                    <button class="btn btn-secondary" onclick="printQR()">🖨️ طباعة</button>
                                    <button class="btn btn-secondary" onclick="shareQR()">📤 مشاركة</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="alertContainer" class="alert"></div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let appState = {
            connectionMethod: 'none', // 'api', 'web', 'none'
            apiSettings: {
                instanceId: '',
                apiToken: ''
            },
            webConnected: false,
            messageQueue: [],
            isProcessing: false,
            stats: {
                sent: 0,
                pending: 0,
                failed: 0
            }
        };

        // Initialize app
        window.onload = function() {
            loadSettings();
            setupEventListeners();
            updateDashboard();
        };

        function loadSettings() {
            const saved = localStorage.getItem('whatsappProSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                appState.apiSettings = settings.apiSettings || appState.apiSettings;
                document.getElementById('instanceId').value = appState.apiSettings.instanceId;
                document.getElementById('apiToken').value = appState.apiSettings.apiToken;
            }
        }

        function saveSettings() {
            localStorage.setItem('whatsappProSettings', JSON.stringify({
                apiSettings: appState.apiSettings,
                connectionMethod: appState.connectionMethod
            }));
        }

        function setupEventListeners() {
            // Menu navigation
            document.querySelectorAll('.menu-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.dataset.section;
                    showSection(section);
                    
                    // Update active menu item
                    document.querySelectorAll('.menu-item').forEach(mi => mi.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        }

        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }
        }

        function updateDashboard() {
            // Update connection status
            const statusElement = document.getElementById('connectionStatus');
            if (appState.connectionMethod === 'api' && appState.apiSettings.instanceId) {
                statusElement.innerHTML = '🤖 متصل عبر API';
                statusElement.style.background = '#d4edda';
                statusElement.style.color = '#155724';
            } else if (appState.connectionMethod === 'web' && appState.webConnected) {
                statusElement.innerHTML = '🌐 متصل عبر WhatsApp Web';
                statusElement.style.background = '#d4edda';
                statusElement.style.color = '#155724';
            } else {
                statusElement.innerHTML = '❌ غير متصل';
                statusElement.style.background = '#f8d7da';
                statusElement.style.color = '#721c24';
            }
        }

        function saveAPISettings() {
            appState.apiSettings.instanceId = document.getElementById('instanceId').value.trim();
            appState.apiSettings.apiToken = document.getElementById('apiToken').value.trim();
            
            if (!appState.apiSettings.instanceId || !appState.apiSettings.apiToken) {
                showAlert('يرجى إدخال Instance ID و API Token', 'error');
                return;
            }
            
            appState.connectionMethod = 'api';
            saveSettings();
            updateDashboard();
            showAlert('تم حفظ إعدادات API بنجاح!', 'success');
        }

        function testAPIConnection() {
            if (!appState.apiSettings.instanceId || !appState.apiSettings.apiToken) {
                showAlert('يرجى حفظ إعدادات API أولاً', 'error');
                return;
            }

            const url = `https://api.green-api.com/waInstance${appState.apiSettings.instanceId}/getSettings/${appState.apiSettings.apiToken}`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.stateInstance === 'authorized') {
                        showAlert('✅ تم الاتصال بنجاح! WhatsApp مرتبط وجاهز للإرسال.', 'success');
                        appState.connectionMethod = 'api';
                        updateDashboard();
                    } else if (data.stateInstance === 'notAuthorized') {
                        showAlert('⚠️ API متصل لكن WhatsApp غير مرتبط. امسح QR Code في لوحة Green API.', 'error');
                    } else {
                        showAlert(`⚠️ حالة الاتصال: ${data.stateInstance}. تحقق من ربط WhatsApp.`, 'error');
                    }
                })
                .catch(error => {
                    showAlert('❌ خطأ في الاتصال: ' + error.message, 'error');
                });
        }

        function connectWhatsAppWeb() {
            // Open WhatsApp Web
            window.open('https://web.whatsapp.com', '_blank', 'width=1200,height=800');
            
            // Update status
            document.getElementById('webConnectionStatus').innerHTML = '🌐 WhatsApp Web مفتوح - امسح QR Code';
            document.getElementById('webConnectionStatus').style.background = '#d1ecf1';
            document.getElementById('webConnectionStatus').style.color = '#0c5460';
            
            // Show disconnect button
            document.getElementById('disconnectWebBtn').style.display = 'inline-block';
            
            // Simulate connection after delay
            setTimeout(() => {
                appState.webConnected = true;
                appState.connectionMethod = 'web';
                updateDashboard();
                showAlert('✅ تم ربط WhatsApp Web بنجاح!', 'success');
            }, 5000);
        }

        function disconnectWhatsAppWeb() {
            appState.webConnected = false;
            appState.connectionMethod = 'none';
            
            document.getElementById('webConnectionStatus').innerHTML = '📱 جاهز للربط';
            document.getElementById('webConnectionStatus').style.background = '#f8f9fa';
            document.getElementById('webConnectionStatus').style.color = '#333';
            
            document.getElementById('disconnectWebBtn').style.display = 'none';
            
            updateDashboard();
            showAlert('تم قطع الاتصال مع WhatsApp Web', 'info');
        }

        function checkConnection() {
            if (appState.connectionMethod === 'api') {
                testAPIConnection();
            } else if (appState.connectionMethod === 'web') {
                showAlert('WhatsApp Web متصل ويعمل بشكل طبيعي', 'success');
            } else {
                showAlert('لم يتم اختيار طريقة اتصال. اذهب لقسم "طرق الاتصال"', 'error');
            }
        }

        function showAlert(message, type) {
            const alert = document.getElementById('alertContainer');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';

            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // Message Templates
        const messageTemplates = {
            welcome: 'مرحباً بك! نحن سعداء لانضمامك إلينا. إذا كان لديك أي استفسار، لا تتردد في التواصل معنا.',
            offer: '🎉 عرض خاص لفترة محدودة! احصل على خصم 30% على جميع خدماتنا. العرض ساري حتى نهاية الشهر.',
            reminder: 'تذكير ودي: لديك موعد معنا غداً. يرجى التأكيد أو إعلامنا في حالة الحاجة لإعادة الجدولة.',
            thanks: 'شكراً لك على ثقتك بنا! نقدر تعاملك معنا ونتطلع لخدمتك مرة أخرى.'
        };

        // Single Message Functions
        function loadTemplate() {
            const template = document.getElementById('messageTemplates').value;
            if (template && messageTemplates[template]) {
                document.getElementById('singleMessage').value = messageTemplates[template];
                showAlert('تم تحميل القالب بنجاح!', 'success');
            }
        }

        function previewMessage() {
            const message = document.getElementById('singleMessage').value;
            if (!message) {
                showAlert('لا توجد رسالة للمعاينة', 'error');
                return;
            }

            alert('معاينة الرسالة:\n\n' + message);
        }

        function clearSingleForm() {
            document.getElementById('singlePhone').value = '';
            document.getElementById('singleMessage').value = '';
            document.getElementById('singleFile').value = '';
            document.getElementById('messageTemplates').value = '';
            showAlert('تم مسح النموذج', 'info');
        }

        function sendSingleMessage() {
            const phone = document.getElementById('singlePhone').value.trim();
            const message = document.getElementById('singleMessage').value.trim();
            const file = document.getElementById('singleFile').files[0];

            if (!phone || !message) {
                showAlert('يرجى إدخال رقم الهاتف والرسالة', 'error');
                return;
            }

            if (appState.connectionMethod === 'none') {
                showAlert('يرجى اختيار طريقة اتصال من قسم "طرق الاتصال"', 'error');
                return;
            }

            // Send message
            sendMessage(phone, message, file)
                .then(result => {
                    showAlert(result, 'success');
                    appState.stats.sent++;
                    updateDashboard();
                })
                .catch(error => {
                    showAlert('فشل الإرسال: ' + error, 'error');
                    appState.stats.failed++;
                    updateDashboard();
                });
        }

        // Bulk Sending Functions
        let bulkState = {
            isRunning: false,
            isPaused: false,
            currentIndex: 0,
            numbers: [],
            message: '',
            results: []
        };

        function validateNumbers() {
            const numbersText = document.getElementById('bulkNumbers').value.trim();
            if (!numbersText) {
                showAlert('لا توجد أرقام للفحص', 'error');
                return;
            }

            const numbers = numbersText.split('\n')
                .map(line => line.trim())
                .filter(line => line);

            const validNumbers = numbers.filter(num => /^\d{10,15}$/.test(num));
            const invalidNumbers = numbers.filter(num => !/^\d{10,15}$/.test(num));

            let message = `تم فحص ${numbers.length} رقم:\n`;
            message += `✅ صحيح: ${validNumbers.length}\n`;
            if (invalidNumbers.length > 0) {
                message += `❌ غير صحيح: ${invalidNumbers.length}`;
            }

            showAlert(message, validNumbers.length > 0 ? 'success' : 'error');
        }

        function startBulkSending() {
            const numbersText = document.getElementById('bulkNumbers').value.trim();
            const message = document.getElementById('bulkMessage').value.trim();
            const delay = parseInt(document.getElementById('delaySeconds').value) || 3;
            const randomize = document.getElementById('randomizeOrder').checked;

            if (!numbersText || !message) {
                showAlert('يرجى إدخال الأرقام والرسالة', 'error');
                return;
            }

            if (appState.connectionMethod === 'none') {
                showAlert('يرجى اختيار طريقة اتصال من قسم "طرق الاتصال"', 'error');
                return;
            }

            let numbers = numbersText.split('\n')
                .map(line => line.trim())
                .filter(line => line && /^\d{10,15}$/.test(line));

            if (numbers.length === 0) {
                showAlert('لا توجد أرقام صحيحة للإرسال', 'error');
                return;
            }

            if (randomize) {
                numbers = shuffleArray(numbers);
            }

            bulkState = {
                isRunning: true,
                isPaused: false,
                currentIndex: 0,
                numbers: numbers,
                message: message,
                delay: delay * 1000,
                results: []
            };

            // Show progress card
            document.getElementById('bulkProgressCard').style.display = 'block';
            document.getElementById('bulkResultsCard').style.display = 'block';

            updateBulkProgress();
            processBulkMessage();
        }

        function processBulkMessage() {
            if (!bulkState.isRunning || bulkState.isPaused) {
                return;
            }

            if (bulkState.currentIndex >= bulkState.numbers.length) {
                // Finished
                bulkState.isRunning = false;
                showAlert(`🎉 تم الانتهاء من الإرسال الجماعي! (${bulkState.results.filter(r => r.status === 'success').length}/${bulkState.numbers.length})`, 'success');
                return;
            }

            const currentNumber = bulkState.numbers[bulkState.currentIndex];
            updateBulkProgress();

            // Send message
            sendMessage(currentNumber, bulkState.message)
                .then(result => {
                    bulkState.results.push({
                        number: currentNumber,
                        status: 'success',
                        message: result
                    });
                    appState.stats.sent++;
                })
                .catch(error => {
                    bulkState.results.push({
                        number: currentNumber,
                        status: 'error',
                        message: error
                    });
                    appState.stats.failed++;
                })
                .finally(() => {
                    updateBulkResults();
                    updateDashboard();
                    bulkState.currentIndex++;

                    // Continue to next message
                    setTimeout(() => {
                        processBulkMessage();
                    }, bulkState.delay);
                });
        }

        function updateBulkProgress() {
            const progress = (bulkState.currentIndex / bulkState.numbers.length) * 100;
            const progressBar = document.getElementById('bulkProgressBar');
            const status = document.getElementById('bulkStatus');

            progressBar.style.width = progress + '%';
            progressBar.textContent = Math.round(progress) + '%';

            status.textContent = `جاري الإرسال: ${bulkState.currentIndex + 1}/${bulkState.numbers.length}`;
        }

        function updateBulkResults() {
            const resultsContainer = document.getElementById('bulkResults');
            resultsContainer.innerHTML = '';

            bulkState.results.forEach(result => {
                const item = document.createElement('div');
                item.className = 'number-item';

                const statusClass = result.status === 'success' ? 'status-success' : 'status-error';
                const statusIcon = result.status === 'success' ? '✅' : '❌';

                item.innerHTML = `
                    <div>
                        <strong>${result.number}</strong>
                        <div style="font-size: 0.9rem; color: #666;">${result.message}</div>
                    </div>
                    <span class="status-badge ${statusClass}">${statusIcon}</span>
                `;

                resultsContainer.appendChild(item);
            });
        }

        function pauseBulkSending() {
            bulkState.isPaused = true;
            showAlert('تم إيقاف الإرسال مؤقتاً', 'info');
        }

        function resumeBulkSending() {
            if (bulkState.isRunning) {
                bulkState.isPaused = false;
                showAlert('تم استئناف الإرسال', 'info');
                processBulkMessage();
            }
        }

        function stopBulkSending() {
            bulkState.isRunning = false;
            bulkState.isPaused = false;
            showAlert('تم إيقاف الإرسال', 'info');
        }

        // File Functions
        let uploadedFiles = [];

        function setupFileUpload() {
            const fileInput = document.getElementById('multipleFiles');
            const uploadArea = document.getElementById('fileUploadArea');

            fileInput.addEventListener('change', handleFileSelect);

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });

            // Distribution method change
            document.getElementById('distributionMethod').addEventListener('change', function() {
                if (this.value === 'manual') {
                    showManualAssignment();
                } else {
                    document.getElementById('manualAssignmentCard').style.display = 'none';
                }
            });
        }

        function handleFileSelect(e) {
            handleFiles(e.target.files);
        }

        function handleFiles(files) {
            for (let file of files) {
                if (file.size > 50 * 1024 * 1024) { // 50MB limit
                    showAlert(`الملف ${file.name} كبير جداً (أكثر من 50MB)`, 'error');
                    continue;
                }

                uploadedFiles.push({
                    file: file,
                    name: file.name,
                    size: formatFileSize(file.size),
                    type: file.type
                });
            }

            displayUploadedFiles();
        }

        function displayUploadedFiles() {
            const container = document.getElementById('uploadedFiles');
            container.innerHTML = '';

            if (uploadedFiles.length === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center;">لا توجد ملفات مرفوعة</p>';
                return;
            }

            uploadedFiles.forEach((fileObj, index) => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-attachment';

                const icon = getFileIcon(fileObj.type);

                fileDiv.innerHTML = `
                    <div class="file-icon">${icon}</div>
                    <div class="file-info">
                        <div class="file-name">${fileObj.name}</div>
                        <div class="file-size">${fileObj.size}</div>
                    </div>
                    <button class="btn btn-danger" onclick="removeFile(${index})" style="padding: 5px 10px;">🗑️</button>
                `;

                container.appendChild(fileDiv);
            });
        }

        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            displayUploadedFiles();
        }

        function getFileIcon(type) {
            if (type.startsWith('image/')) return '🖼️';
            if (type.startsWith('video/')) return '🎥';
            if (type.startsWith('audio/')) return '🎵';
            if (type.includes('pdf')) return '📄';
            if (type.includes('word') || type.includes('document')) return '📝';
            return '📎';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // QR Code Functions
        function generateQRCode() {
            const phone = document.getElementById('qrPhone').value.trim();
            const message = document.getElementById('qrMessage').value.trim();
            const size = parseInt(document.getElementById('qrSize').value);

            if (!phone || !message) {
                showAlert('يرجى إدخال رقم الهاتف والرسالة', 'error');
                return;
            }

            const whatsappUrl = `https://wa.me/${phone.replace(/\D/g, '')}?text=${encodeURIComponent(message)}`;

            // Generate QR Code using Google Charts API
            const canvas = document.getElementById('qrCanvas');
            const ctx = canvas.getContext('2d');

            canvas.width = size;
            canvas.height = size;

            const qrUrl = `https://chart.googleapis.com/chart?chs=${size}x${size}&cht=qr&chl=${encodeURIComponent(whatsappUrl)}&choe=UTF-8`;

            const img = new Image();
            img.crossOrigin = 'anonymous';

            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);

                // Show QR Code
                document.getElementById('qrPlaceholder').style.display = 'none';
                canvas.style.display = 'block';
                document.getElementById('qrActions').style.display = 'block';
                document.getElementById('qrContainer').classList.add('active');

                showAlert('تم إنشاء QR Code بنجاح!', 'success');
            };

            img.onerror = function() {
                showAlert('فشل في إنشاء QR Code', 'error');
            };

            img.src = qrUrl;
        }

        function downloadQR() {
            const canvas = document.getElementById('qrCanvas');
            const link = document.createElement('a');
            link.download = 'whatsapp-qr-code.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        function printQR() {
            const canvas = document.getElementById('qrCanvas');
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head><title>طباعة QR Code</title></head>
                    <body style="text-align: center; padding: 20px;">
                        <h2>WhatsApp QR Code</h2>
                        <img src="${canvas.toDataURL()}" style="max-width: 100%;">
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function shareQR() {
            const canvas = document.getElementById('qrCanvas');
            canvas.toBlob(blob => {
                if (navigator.share) {
                    navigator.share({
                        title: 'WhatsApp QR Code',
                        files: [new File([blob], 'whatsapp-qr.png', { type: 'image/png' })]
                    });
                } else {
                    downloadQR();
                }
            });
        }

        // Core sending function
        function sendMessage(phone, message, file = null) {
            return new Promise((resolve, reject) => {
                if (appState.connectionMethod === 'api') {
                    sendViaAPI(phone, message, file).then(resolve).catch(reject);
                } else if (appState.connectionMethod === 'web') {
                    sendViaWeb(phone, message, file).then(resolve).catch(reject);
                } else {
                    reject('لم يتم اختيار طريقة اتصال');
                }
            });
        }

        function sendViaAPI(phone, message, file) {
            return new Promise((resolve, reject) => {
                const cleanPhone = phone.replace(/\D/g, '') + '@c.us';
                const url = `https://api.green-api.com/waInstance${appState.apiSettings.instanceId}/sendMessage/${appState.apiSettings.apiToken}`;

                fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        chatId: cleanPhone,
                        message: message
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.idMessage) {
                        resolve(`✅ تم الإرسال بنجاح (ID: ${data.idMessage})`);
                    } else {
                        reject(data.error || 'فشل الإرسال');
                    }
                })
                .catch(error => reject(error.message));
            });
        }

        function sendViaWeb(phone, message, file) {
            return new Promise((resolve) => {
                const cleanPhone = phone.replace(/\D/g, '');
                const whatsappUrl = `https://web.whatsapp.com/send?phone=${cleanPhone}&text=${encodeURIComponent(message)}`;

                window.open(whatsappUrl, '_blank');
                resolve('✅ تم فتح WhatsApp Web مع الرسالة جاهزة');
            });
        }

        // Utility functions
        function shuffleArray(array) {
            const newArray = [...array];
            for (let i = newArray.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
            }
            return newArray;
        }

        // Initialize file upload when page loads
        window.addEventListener('load', () => {
            setupFileUpload();
        });
    </script>
</body>
</html>
