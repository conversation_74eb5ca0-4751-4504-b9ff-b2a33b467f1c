# 📱 أداة إرسال رسائل WhatsApp

أداة بسيطة وفعالة لإرسال رسائل WhatsApp من خلال Node.js باستخدام مكتبة `whatsapp-web.js`.

## ✨ المميزات

- 🔐 **آمنة ومحمية**: تستخدم WhatsApp Web API الرسمي
- 📱 **سهلة الاستخدام**: واجهة تفاعلية بسيطة
- 🚀 **سريعة**: إرسال رسائل فورية
- 📊 **إرسال متعدد**: إمكانية إرسال رسالة لعدة أرقام
- ✅ **التحقق من الأرقام**: فحص صحة الأرقام قبل الإرسال
- 🔄 **حفظ الجلسة**: لا حاجة لمسح QR Code في كل مرة

## 📋 المتطلبات

- Node.js (الإصدار 14 أو أحدث)
- npm (مدير الحزم)
- اتصال بالإنترنت
- هاتف ذكي مع WhatsApp

## 🚀 التثبيت والتشغيل

### 1. تثبيت المكتبات (تم بالفعل)
```bash
npm install whatsapp-web.js qrcode-terminal readline-sync
```

### 2. تشغيل البرنامج
```bash
node whatsapp-sender.js
```

### 3. ربط الجهاز
1. ستظهر لك رمز QR Code في الطرفية
2. افتح WhatsApp على هاتفك
3. اذهب إلى: **الإعدادات** > **الأجهزة المرتبطة** > **ربط جهاز**
4. امسح رمز QR Code المعروض
5. انتظر حتى يتم تسجيل الدخول بنجاح

## 📖 كيفية الاستخدام

### إرسال رسالة واحدة
1. اختر الخيار `1` من القائمة
2. أدخل رقم الهاتف (مثال: `01234567890`)
3. أدخل نص الرسالة
4. اضغط Enter للإرسال

### إرسال رسالة لعدة أرقام
1. اختر الخيار `2` من القائمة
2. أدخل الأرقام مفصولة بفاصلة (مثال: `01234567890,01987654321`)
3. أدخل نص الرسالة
4. اضغط Enter للإرسال

## 📝 تنسيق الأرقام

الأداة تدعم تنسيقات مختلفة للأرقام:
- `01234567890` (رقم مصري بدون رمز الدولة)
- `201234567890` (رقم مصري مع رمز الدولة)
- `+201234567890` (رقم مصري مع رمز الدولة والعلامة)

## ⚠️ تحذيرات مهمة

1. **لا تستخدم الأداة للرسائل المزعجة (Spam)**
2. **احترم خصوصية الآخرين**
3. **تأكد من موافقة المستقبلين على الرسائل**
4. **لا تشارك معلومات حساسة**
5. **استخدم الأداة بمسؤولية**

## 🔧 استكشاف الأخطاء

### مشكلة: لا يظهر QR Code
- تأكد من تثبيت جميع المكتبات
- أعد تشغيل البرنامج

### مشكلة: فشل في إرسال الرسالة
- تحقق من صحة رقم الهاتف
- تأكد من أن الرقم مسجل في WhatsApp
- تحقق من اتصال الإنترنت

### مشكلة: انقطاع الاتصال
- أعد تشغيل البرنامج
- تأكد من استقرار اتصال الإنترنت

## 📁 ملفات المشروع

```
whats-app-hacker/
├── package.json          # معلومات المشروع والمكتبات
├── whatsapp-sender.js    # الملف الرئيسي للبرنامج
├── README.md            # دليل الاستخدام
└── .wwebjs_auth/        # مجلد حفظ بيانات الجلسة (يتم إنشاؤه تلقائياً)
```

## 🛡️ الأمان والخصوصية

- الأداة تستخدم WhatsApp Web API الرسمي
- لا يتم حفظ أو تسجيل الرسائل
- بيانات الجلسة محفوظة محلياً فقط
- لا يتم إرسال أي بيانات لخوادم خارجية

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يمكنك:
- مراجعة قسم استكشاف الأخطاء أعلاه
- التأكد من تحديث Node.js إلى أحدث إصدار
- إعادة تثبيت المكتبات

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

---

**ملاحظة**: يرجى استخدام هذه الأداة بمسؤولية واحترام قوانين وسياسات WhatsApp.
