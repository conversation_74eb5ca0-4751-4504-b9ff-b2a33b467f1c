@echo off
title اختبار الطرق المجانية - WhatsApp Sender Pro
color 0B

echo.
echo ==========================================
echo    اختبار الطرق المجانية - WhatsApp Sender Pro
echo ==========================================
echo.

echo 🆓 تم إضافة 3 طرق مجانية لإرسال رسائل WhatsApp!
echo.

echo الطرق المجانية المتوفرة:
echo.
echo 🔗 الطريقة الأولى - الروابط المباشرة:
echo ✅ إنشاء روابط wa.me مباشرة
echo ✅ فتح WhatsApp مع الرسالة جاهزة
echo ✅ نسخ ومشاركة الروابط
echo ✅ سهولة استخدام قصوى
echo ✅ مجاني 100%% - لا يحتاج API
echo.
echo 🌐 الطريقة الثانية - WhatsApp Web:
echo ✅ إرسال جماعي عبر WhatsApp Web
echo ✅ فتح نوافذ متعددة تلقائياً
echo ✅ تحكم في الإرسال (إيقاف/استئناف)
echo ✅ مثالي للإرسال لعدد كبير
echo ✅ مجاني 100%% - استخدام WhatsApp العادي
echo.
echo 🤖 الطريقة الثالثة - APIs مجانية:
echo ✅ CallMeBot API - مجاني تماماً
echo ✅ WhatsApp Business API - مجاني للاستخدام المحدود
echo ✅ إرسال تلقائي بدون تدخل
echo ✅ مثالي للتطبيقات والأتمتة
echo ✅ مجاني مع حدود استخدام معقولة
echo.

echo جاري فتح التطبيق مع الطرق المجانية...
echo.

REM فتح التطبيق المحدث
start "" "WhatsApp-Sender-Pro.html"

echo ✅ تم فتح WhatsApp Sender Pro!
echo.

echo خطوات اختبار الطرق المجانية:
echo.

echo 🔗 اختبار الروابط المباشرة:
echo 1️⃣ اضغط على تبويب "🆓 إرسال مجاني"
echo 2️⃣ اضغط "🔗 استخدام الروابط المباشرة"
echo 3️⃣ أدخل رقم هاتف: 201234567890
echo 4️⃣ اكتب رسالة: "مرحباً! هذا اختبار للروابط المباشرة"
echo 5️⃣ اضغط "🔗 إنشاء الرابط"
echo 6️⃣ اضغط "📱 إرسال مباشر" أو "📋 نسخ الرابط"
echo 7️⃣ سيفتح WhatsApp مع الرسالة جاهزة
echo.

echo 🌐 اختبار WhatsApp Web:
echo 1️⃣ اضغط "🌐 استخدام WhatsApp Web"
echo 2️⃣ أدخل أرقام متعددة (رقم في كل سطر):
echo    201234567890
echo    201987654321
echo    201555666777
echo 3️⃣ اكتب رسالة: "مرحباً! هذا اختبار للإرسال الجماعي"
echo 4️⃣ اضغط "🌐 إرسال عبر WhatsApp Web"
echo 5️⃣ ستفتح نوافذ WhatsApp Web تلقائياً
echo 6️⃣ يمكنك إيقاف/استئناف الإرسال
echo.

echo 🤖 اختبار APIs المجانية:
echo.
echo للـ CallMeBot API:
echo 1️⃣ اضغط "🤖 استخدام APIs مجانية"
echo 2️⃣ اذهب لـ callmebot.com واحصل على API Key
echo 3️⃣ أدخل رقم هاتفك مع كود الدولة: +201234567890
echo 4️⃣ أدخل API Key الذي حصلت عليه
echo 5️⃣ اكتب رسالة واضغط "📤 إرسال"
echo.
echo للـ WhatsApp Business API:
echo 1️⃣ اذهب لـ developers.facebook.com/docs/whatsapp
echo 2️⃣ أنشئ تطبيق WhatsApp Business
echo 3️⃣ احصل على Phone Number ID و Access Token
echo 4️⃣ أدخل البيانات في التطبيق
echo 5️⃣ اكتب رسالة واضغط "📤 إرسال"
echo.

echo مميزات كل طريقة:
echo.

echo 🔗 الروابط المباشرة:
echo ✅ الأسهل والأسرع
echo ✅ لا يحتاج تسجيل أو API
echo ✅ يعمل فوراً
echo ✅ مثالي للرسائل الفردية
echo ✅ يمكن مشاركة الروابط
echo.

echo 🌐 WhatsApp Web:
echo ✅ مثالي للإرسال الجماعي
echo ✅ تحكم كامل في العملية
echo ✅ يستخدم WhatsApp العادي
echo ✅ لا يحتاج API أو تسجيل
echo ✅ يمكن إيقاف/استئناف الإرسال
echo.

echo 🤖 APIs المجانية:
echo ✅ إرسال تلقائي كامل
echo ✅ مثالي للتطبيقات
echo ✅ لا يحتاج تدخل يدوي
echo ✅ يمكن دمجه مع أنظمة أخرى
echo ✅ إحصائيات وتتبع
echo.

echo نصائح للاستخدام الأمثل:
echo.
echo 💡 للاستخدام الشخصي:
echo • استخدم الروابط المباشرة للرسائل القليلة
echo • استخدم WhatsApp Web للإرسال الجماعي
echo.
echo 💡 للاستخدام التجاري:
echo • ابدأ بـ CallMeBot للاختبار
echo • انتقل لـ WhatsApp Business API للاستخدام الكثيف
echo • استخدم WhatsApp Web للحملات المتوسطة
echo.
echo 💡 للمطورين:
echo • استخدم APIs المجانية للتطبيقات
echo • ادمج الروابط المباشرة في المواقع
echo • استخدم WhatsApp Web للأدوات الداخلية
echo.

echo مقارنة التكلفة:
echo.
echo 🆓 الروابط المباشرة: مجاني تماماً
echo 🆓 WhatsApp Web: مجاني تماماً
echo 🆓 CallMeBot: مجاني مع حد يومي
echo 🆓 WhatsApp Business: مجاني لـ 1000 رسالة/شهر
echo 💰 Green API: مدفوع من البداية
echo.

echo علامات النجاح:
echo.
echo ✅ للروابط المباشرة:
echo • ظهور الرابط في الحقل
echo • فتح WhatsApp مع الرسالة معبأة
echo • إمكانية نسخ ومشاركة الرابط
echo.
echo ✅ لـ WhatsApp Web:
echo • فتح نوافذ WhatsApp Web تلقائياً
echo • ظهور حالة الإرسال
echo • عمل أزرار التحكم (إيقاف/استئناف)
echo.
echo ✅ للـ APIs المجانية:
echo • رسالة "تم الإرسال بنجاح"
echo • وصول الرسالة للمستقبل
echo • ظهور ID الرسالة (للـ Business API)
echo.

echo ==========================================
echo اضغط أي زر لإغلاق هذه النافذة
echo ==========================================
pause >nul
exit
