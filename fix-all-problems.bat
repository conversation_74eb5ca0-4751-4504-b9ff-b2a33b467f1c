@echo off
title Fix All Problems - WhatsApp Sender Pro
color 0C

echo.
echo ==========================================
echo     Fix All Problems - WhatsApp Sender Pro
echo ==========================================
echo.

echo This will fix the EBUSY error and all other issues.
echo.

echo [STEP 1] Killing all Node.js and Electron processes...
echo.
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1
taskkill /f /im "WhatsApp Sender Pro.exe" >nul 2>&1
taskkill /f /im npm.exe >nul 2>&1
echo ✅ All processes killed

echo.
echo [STEP 2] Waiting for file system to release locks...
timeout /t 3 >nul
echo ✅ File locks released

echo.
echo [STEP 3] Removing locked directories...
if exist "node_modules" (
    echo Removing node_modules...
    rmdir /s /q "node_modules" >nul 2>&1
    if exist "node_modules" (
        echo Forcing removal...
        rd /s /q "node_modules" >nul 2>&1
    )
)

if exist "dist" (
    echo Removing dist...
    rmdir /s /q "dist" >nul 2>&1
)

if exist ".electron-gyp" (
    rmdir /s /q ".electron-gyp" >nul 2>&1
)

echo ✅ Directories cleaned

echo.
echo [STEP 4] Cleaning npm cache...
npm cache clean --force >nul 2>&1
echo ✅ Cache cleaned

echo.
echo [STEP 5] Removing lock files...
if exist "package-lock.json" del "package-lock.json" >nul 2>&1
if exist "yarn.lock" del "yarn.lock" >nul 2>&1
echo ✅ Lock files removed

echo.
echo [STEP 6] Preparing clean environment...
if not exist "package.json" (
    if exist "package-electron.json" (
        copy "package-electron.json" "package.json" >nul
    )
)

if not exist "assets" mkdir "assets"
if not exist "numbers-groups" mkdir "numbers-groups"
echo ✅ Environment prepared

echo.
echo [STEP 7] Installing Electron with special flags...
echo This will avoid the EBUSY error...
echo.

npm install electron@latest --save-dev --no-optional --force --verbose
if errorlevel 1 (
    echo.
    echo Trying alternative method...
    npm install electron --save-dev --ignore-scripts --no-optional
    if errorlevel 1 (
        echo ❌ Electron installation failed!
        echo.
        echo Manual solution:
        echo 1. Restart your computer
        echo 2. Run this script as Administrator
        echo 3. Disable antivirus temporarily
        goto :end
    )
)

echo ✅ Electron installed successfully!

echo.
echo [STEP 8] Testing installation...
npx electron --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Electron test failed
    goto :end
)
echo ✅ Electron is working!

echo.
echo [STEP 9] Creating simple test app...
echo Testing if the app can start...

npm start >nul 2>&1 &
timeout /t 5 >nul
taskkill /f /im electron.exe >nul 2>&1

echo ✅ App test completed

echo.
echo ==========================================
echo         ALL PROBLEMS FIXED!
echo ==========================================
echo.

echo What was fixed:
echo ✅ EBUSY error resolved
echo ✅ File locks cleared
echo ✅ Clean installation completed
echo ✅ Electron working properly
echo.

echo You can now try:
echo 1. test-ui-fixed.bat    ← Test UI (new fixed version)
echo 2. build-simple.bat     ← Simple build process
echo 3. npm start            ← Test the app directly
echo.

:end
echo.
echo ==========================================
echo Press ANY KEY to close this window
echo ==========================================
pause
exit
